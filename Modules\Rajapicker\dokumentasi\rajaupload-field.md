# RajaUpload Field - Custom FileUpload Component

RajaUpload adalah custom field FilamentPHP yang extend dari FileUpload dengan fitur-fitur tambahan yang terintegrasi dengan sistem RajaPicker.

## Fitur Utama

- **Extend dari FileUpload**: Semua fitur FileUpload FilamentPHP tersedia
- **Integrasi RajaPicker**: Menggunakan konfigurasi dan service dari RajaPicker
- **Auto Thumbnail Generation**: Otomatis generate thumbnail sesuai konfigurasi
- **WebP Conversion**: Konversi otomatis ke format WebP
- **Custom File Naming**: Penamaan file sesuai pattern konfigurasi
- **Collection Support**: Mendukung berbagai collection dengan konfigurasi berbeda
- **Preset Methods**: Method siap pakai untuk berbagai kebutuhan

## Instalasi dan Setup

RajaUpload sudah tersedia di modul RajaPicker, tidak perlu instalasi tambahan.

```php
use Modules\Rajapicker\Filament\Forms\Components\RajaUpload;
```

## Pengg<PERSON>an Dasar

### Upload Gambar Sederhana
```php
RajaUpload::make('image')
    ->label('Gambar')
    ->collection('default')
```

### Upload Multiple dengan Gallery
```php
RajaUpload::make('gallery')
    ->label('Galeri Foto')
    ->gallery(10) // max 10 files
    ->collection('gallery')
```

### Upload Avatar
```php
RajaUpload::make('avatar')
    ->label('Avatar')
    ->avatar()
    ->collection('avatars')
```

## Method Konfigurasi

### Collection dan Storage
```php
RajaUpload::make('image')
    ->collection('produk')           // Set collection
    ->generateThumbnails()           // Enable thumbnail generation
    ->convertToWebp()                // Enable WebP conversion
    ->autoSetUserId()                // Auto set user_id
```

### Image Editor dan Cropping
```php
RajaUpload::make('image')
    ->imageEditor()                  // Enable image editor
    ->commonAspectRatios()          // Set common aspect ratios
    ->socialMediaAspectRatios()     // Set social media ratios
    ->circleCropper()               // Enable circle cropper
```

### Image Resize Presets
```php
RajaUpload::make('image')
    ->imageResizePreset('medium')   // small, medium, large, hd, 4k
    ->imageResizePreset('hd')       // 1920x1080
```

### Custom Thumbnail Sizes
```php
RajaUpload::make('image')
    ->thumbnailSizes([
        'small' => ['width' => 150, 'height' => 150],
        'medium' => ['width' => 300, 'height' => 300],
        'large' => ['width' => 600, 'height' => 600],
    ])
```

## Preset Methods (Quick Setup)

### Product Image
```php
RajaUpload::make('product_image')
    ->productImage()  // Auto setup untuk gambar produk
```
Setara dengan:
```php
RajaUpload::make('product_image')
    ->collection('produk')
    ->imageResizePreset('medium')
    ->imageEditor()
    ->commonAspectRatios()
    ->generateThumbnails()
    ->convertToWebp()
```

### Gallery Images
```php
RajaUpload::make('gallery_images')
    ->galleryImages(20)  // max 20 files
```

### Banner Image
```php
RajaUpload::make('banner')
    ->bannerImage()  // Optimized untuk banner/hero image
```

### User Avatar
```php
RajaUpload::make('avatar')
    ->userAvatar()  // Optimized untuk avatar user
```

### Document Upload
```php
RajaUpload::make('documents')
    ->document()  // Setup untuk upload dokumen (PDF, Word, Excel, dll)
```

## Aspect Ratios

### Common Aspect Ratios
```php
->commonAspectRatios()
```
Menyediakan: Free crop, 16:9, 4:3, 3:2, 1:1, 9:16

### Social Media Aspect Ratios
```php
->socialMediaAspectRatios()
```
Menyediakan: 1:1 (Instagram), 16:9 (Facebook cover), 9:16 (Story), 4:5 (Instagram portrait), 1.91:1 (Facebook post)

### Custom Aspect Ratios
```php
->imageEditorAspectRatios([
    null,    // free crop
    '16:9',  // widescreen
    '1:1',   // square
    '4:3',   // standard
])
```

## Contoh Implementasi Lengkap

### Form Resource dengan Berbagai Upload
```php
use Modules\Rajapicker\Filament\Forms\Components\RajaUpload;

public static function form(Form $form): Form
{
    return $form->schema([
        // Avatar user
        RajaUpload::make('avatar')
            ->label('Avatar')
            ->userAvatar(),
            
        // Gambar produk
        RajaUpload::make('product_images')
            ->label('Gambar Produk')
            ->productImage()
            ->multiple()
            ->maxFiles(5),
            
        // Banner/hero image
        RajaUpload::make('banner')
            ->label('Banner')
            ->bannerImage(),
            
        // Galeri foto
        RajaUpload::make('gallery')
            ->label('Galeri')
            ->galleryImages(15),
            
        // Dokumen pendukung
        RajaUpload::make('documents')
            ->label('Dokumen')
            ->document()
            ->multiple()
            ->maxFiles(3),
    ]);
}
```

### Custom Configuration
```php
RajaUpload::make('custom_image')
    ->label('Custom Image')
    ->collection('custom')
    ->disk('s3')
    ->directory('custom-uploads')
    ->acceptedFileTypes(['image/jpeg', 'image/png'])
    ->maxSize(5120) // 5MB
    ->imageEditor()
    ->imageEditorAspectRatios(['16:9', '4:3'])
    ->imageResizePreset('large')
    ->generateThumbnails()
    ->convertToWebp()
    ->thumbnailSizes([
        'thumb' => ['width' => 150, 'height' => 150],
        'medium' => ['width' => 400, 'height' => 300],
    ])
```

## Integrasi dengan Model

### Eloquent Cast untuk Multiple Files
```php
// Model
protected $casts = [
    'gallery_images' => 'array',
    'documents' => 'array',
];
```

### Accessor untuk URL
```php
// Model
public function getAvatarUrlAttribute()
{
    return $this->avatar ? Storage::url($this->avatar) : '/noimage.jpg';
}

public function getGalleryUrlsAttribute()
{
    if (!$this->gallery_images) return [];
    
    return collect($this->gallery_images)->map(function ($path) {
        return Storage::url($path);
    })->toArray();
}
```

## Tips dan Best Practices

1. **Gunakan Collection yang Tepat**: Setiap jenis upload sebaiknya menggunakan collection yang berbeda
2. **Optimasi Performa**: Aktifkan thumbnail generation dan WebP conversion untuk performa yang lebih baik
3. **Validasi File**: Selalu set acceptedFileTypes dan maxSize sesuai kebutuhan
4. **Preset Methods**: Gunakan preset methods untuk setup yang lebih cepat dan konsisten
5. **Storage Disk**: Pertimbangkan menggunakan S3 atau cloud storage untuk file yang besar

## Troubleshooting

### File Tidak Ter-upload
- Periksa permission direktori storage
- Pastikan disk storage sudah dikonfigurasi dengan benar
- Cek ukuran file tidak melebihi limit PHP dan Livewire

### Thumbnail Tidak Generate
- Pastikan Intervention Image sudah terinstall
- Periksa konfigurasi thumbnail di config/rajapicker.php
- Cek log error untuk detail masalah

### WebP Conversion Gagal
- Pastikan GD atau Imagick extension mendukung WebP
- Periksa konfigurasi WebP di config file
- Cek permission direktori output
