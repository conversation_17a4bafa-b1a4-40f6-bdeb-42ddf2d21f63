<?php

namespace Modules\Rajapicker\Services;

use Illuminate\Support\Facades\Config;
use Illuminate\Support\Str;

class RajaUploadConfigService
{
    protected array $config;

    public function __construct()
    {
        $this->config = Config::get('rajapicker.rajaupload', []);
    }

    /**
     * Get storage disk name
     */
    public function getStorageDisk(): string
    {
        return $this->config['storage']['disk'] ?? 'public';
    }

    /**
     * Get base storage path
     */
    public function getBasePath(): string
    {
        return $this->config['storage']['base_path'] ?? 'uploads';
    }

    /**
     * Get URL prefix
     */
    public function getUrlPrefix(): string
    {
        return $this->config['storage']['url_prefix'] ?? '/storage';
    }

    /**
     * Get max file size in KB
     */
    public function getMaxFileSize(): int
    {
        return $this->config['upload']['max_file_size'] ?? 10240;
    }

    /**
     * Get max files count
     */
    public function getMaxFiles(): int
    {
        return $this->config['upload']['max_files'] ?? 10;
    }

    /**
     * Get chunk size for upload
     */
    public function getChunkSize(): int
    {
        return $this->config['upload']['chunk_size'] ?? 1024;
    }

    /**
     * Get temporary directory
     */
    public function getTempDirectory(): string
    {
        return $this->config['upload']['temporary_directory'] ?? 'livewire-tmp';
    }

    /**
     * Get file naming pattern
     */
    public function getNamingPattern(): string
    {
        return $this->config['naming']['pattern'] ?? '{timestamp}_{random}_{original}';
    }

    /**
     * Get timestamp format for naming
     */
    public function getTimestampFormat(): string
    {
        return $this->config['naming']['timestamp_format'] ?? 'YmdHis';
    }

    /**
     * Get random string length for naming
     */
    public function getRandomLength(): int
    {
        return $this->config['naming']['random_length'] ?? 8;
    }

    /**
     * Check if original filename should be preserved
     */
    public function shouldPreserveOriginal(): bool
    {
        return $this->config['naming']['preserve_original'] ?? true;
    }

    /**
     * Check if filename should be lowercase
     */
    public function shouldLowercase(): bool
    {
        return $this->config['naming']['lowercase'] ?? true;
    }

    /**
     * Check if spaces should be removed from filename
     */
    public function shouldRemoveSpaces(): bool
    {
        return $this->config['naming']['remove_spaces'] ?? true;
    }

    /**
     * Get allowed characters pattern
     */
    public function getAllowedCharacters(): string
    {
        return $this->config['naming']['allowed_characters'] ?? 'a-zA-Z0-9._-';
    }

    /**
     * Get image driver
     */
    public function getImageDriver(): string
    {
        return $this->config['image']['driver'] ?? 'gd';
    }

    /**
     * Get image quality
     */
    public function getImageQuality(): int
    {
        return $this->config['image']['quality'] ?? 85;
    }

    /**
     * Check if auto orient is enabled
     */
    public function isAutoOrientEnabled(): bool
    {
        return $this->config['image']['auto_orient'] ?? true;
    }

    /**
     * Check if metadata should be stripped
     */
    public function shouldStripMetadata(): bool
    {
        return $this->config['image']['strip_metadata'] ?? true;
    }

    /**
     * Check if progressive JPEG is enabled
     */
    public function isProgressiveEnabled(): bool
    {
        return $this->config['image']['progressive'] ?? true;
    }

    /**
     * Check if thumbnails are enabled
     */
    public function isThumbnailsEnabled(): bool
    {
        return $this->config['thumbnails']['enabled'] ?? true;
    }

    /**
     * Get thumbnails directory
     */
    public function getThumbnailsDirectory(): string
    {
        return $this->config['thumbnails']['directory'] ?? 'thumbnails';
    }

    /**
     * Get thumbnail prefix
     */
    public function getThumbnailPrefix(): string
    {
        return $this->config['thumbnails']['prefix'] ?? 'thumb_';
    }

    /**
     * Get thumbnail suffix
     */
    public function getThumbnailSuffix(): string
    {
        return $this->config['thumbnails']['suffix'] ?? '';
    }

    /**
     * Get thumbnail quality
     */
    public function getThumbnailQuality(): int
    {
        return $this->config['thumbnails']['quality'] ?? 80;
    }

    /**
     * Get thumbnail fit method
     */
    public function getThumbnailFit(): string
    {
        return $this->config['thumbnails']['fit'] ?? 'crop';
    }

    /**
     * Get thumbnail sizes configuration
     */
    public function getThumbnailSizes(): array
    {
        return $this->config['thumbnails']['sizes'] ?? [];
    }

    /**
     * Get specific thumbnail size configuration
     */
    public function getThumbnailSize(string $size): ?array
    {
        return $this->config['thumbnails']['sizes'][$size] ?? null;
    }

    /**
     * Get thumbnail size names
     */
    public function getThumbnailSizeNames(): array
    {
        return array_keys($this->config['thumbnails']['sizes'] ?? []);
    }

    /**
     * Check if WebP is enabled
     */
    public function isWebpEnabled(): bool
    {
        return $this->config['webp']['enabled'] ?? true;
    }

    /**
     * Get WebP quality
     */
    public function getWebpQuality(): int
    {
        return $this->config['webp']['quality'] ?? 80;
    }

    /**
     * Check if WebP original should be generated
     */
    public function shouldGenerateWebpOriginal(): bool
    {
        return $this->config['webp']['generate_original'] ?? true;
    }

    /**
     * Check if WebP thumbnails should be generated
     */
    public function shouldGenerateWebpThumbnails(): bool
    {
        return $this->config['webp']['generate_thumbnails'] ?? true;
    }

    /**
     * Get WebP suffix
     */
    public function getWebpSuffix(): string
    {
        return $this->config['webp']['suffix'] ?? '.webp';
    }

    /**
     * Get file type configuration
     */
    public function getFileTypeConfig(string $type): ?array
    {
        return $this->config['file_types'][$type] ?? null;
    }

    /**
     * Get allowed extensions for file type
     */
    public function getAllowedExtensions(string $type): array
    {
        return $this->config['file_types'][$type]['extensions'] ?? [];
    }

    /**
     * Get allowed MIME types for file type
     */
    public function getAllowedMimeTypes(string $type): array
    {
        return $this->config['file_types'][$type]['mime_types'] ?? [];
    }

    /**
     * Get max size for file type
     */
    public function getMaxSizeForType(string $type): int
    {
        return $this->config['file_types'][$type]['max_size'] ?? $this->getMaxFileSize();
    }

    /**
     * Get collection configuration
     */
    public function getCollectionConfig(string $collection): ?array
    {
        return $this->config['collections'][$collection] ?? null;
    }

    /**
     * Get all collections
     */
    public function getAllCollections(): array
    {
        return $this->config['collections'] ?? [];
    }

    /**
     * Get collection directory
     */
    public function getCollectionDirectory(string $collection): string
    {
        return $this->config['collections'][$collection]['directory'] ?? $collection;
    }

    /**
     * Get collection max files
     */
    public function getCollectionMaxFiles(string $collection): int
    {
        return $this->config['collections'][$collection]['max_files'] ?? $this->getMaxFiles();
    }

    /**
     * Get collection file types
     */
    public function getCollectionFileTypes(string $collection): array
    {
        return $this->config['collections'][$collection]['file_types'] ?? ['images'];
    }

    /**
     * Get collection thumbnails
     */
    public function getCollectionThumbnails(string $collection): array
    {
        $thumbnails = $this->config['collections'][$collection]['thumbnails'] ?? ['small', 'medium'];
        return is_array($thumbnails) ? $thumbnails : [];
    }

    /**
     * Check if WebP is enabled for collection
     */
    public function isWebpEnabledForCollection(string $collection): bool
    {
        return $this->config['collections'][$collection]['webp'] ?? $this->isWebpEnabled();
    }

    /**
     * Get collection naming pattern
     */
    public function getCollectionNamingPattern(string $collection): string
    {
        return $this->config['collections'][$collection]['naming_pattern'] ?? $this->getNamingPattern();
    }

    /**
     * Get collection resize configuration
     */
    public function getCollectionResize(string $collection): ?array
    {
        return $this->config['collections'][$collection]['resize'] ?? null;
    }

    /**
     * Check if collection is reorderable
     */
    public function isCollectionReorderable(string $collection): bool
    {
        return $this->config['collections'][$collection]['reorderable'] ?? false;
    }

    /**
     * Generate filename based on pattern
     */
    public function generateFilename(string $originalName, string $pattern = null, array $replacements = []): string
    {
        $pattern = $pattern ?? $this->getNamingPattern();
        $pathInfo = pathinfo($originalName);
        
        $replacements = array_merge([
            'timestamp' => now()->format($this->getTimestampFormat()),
            'random' => Str::random($this->getRandomLength()),
            'original' => $pathInfo['filename'],
            'extension' => $pathInfo['extension'] ?? '',
        ], $replacements);

        $filename = $pattern;
        foreach ($replacements as $key => $value) {
            $filename = str_replace('{' . $key . '}', $value, $filename);
        }

        // Apply naming rules
        if ($this->shouldRemoveSpaces()) {
            $filename = str_replace(' ', '_', $filename);
        }

        if ($this->shouldLowercase()) {
            $filename = strtolower($filename);
        }

        // Clean filename
        $allowedChars = $this->getAllowedCharacters();
        $filename = preg_replace('/[^' . $allowedChars . ']/', '', $filename);

        // Add extension
        if (!empty($pathInfo['extension'])) {
            $filename .= '.' . $pathInfo['extension'];
        }

        return $filename;
    }

    /**
     * Generate full file path for collection
     */
    public function generateFilePath(string $collection, string $filename): string
    {
        $basePath = $this->getBasePath();
        $collectionDir = $this->getCollectionDirectory($collection);
        
        return $basePath . '/' . $collectionDir . '/' . $filename;
    }

    /**
     * Generate file URL
     */
    public function generateFileUrl(string $filePath): string
    {
        $urlPrefix = $this->getUrlPrefix();
        return $urlPrefix . '/' . $filePath;
    }

    /**
     * Check if logging is enabled
     */
    public function isLoggingEnabled(): bool
    {
        return $this->config['logging']['enabled'] ?? true;
    }

    /**
     * Get log level
     */
    public function getLogLevel(): string
    {
        return $this->config['logging']['level'] ?? 'info';
    }

    /**
     * Check if auto cleanup is enabled
     */
    public function isAutoCleanupEnabled(): bool
    {
        return $this->config['cleanup']['auto_cleanup'] ?? true;
    }

    /**
     * Get temp file lifetime in hours
     */
    public function getTempFileLifetime(): int
    {
        return $this->config['cleanup']['temp_file_lifetime'] ?? 24;
    }
}
