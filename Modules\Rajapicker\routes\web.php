<?php

use Illuminate\Support\Facades\Route;
use <PERSON><PERSON><PERSON>\Rajapicker\Http\Controllers\RajapickerController;
use Mo<PERSON><PERSON>\Rajapicker\Http\Controllers\GambarController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

Route::group([], function () {
    Route::resource('rajapicker', RajapickerController::class)->names('rajapicker');

    // Routes untuk menampilkan gambar berdasarkan ID
    Route::get('rajapicker/{id}/image', [RajapickerController::class, 'showImage'])->name('rajapicker.image');
    Route::get('rajapicker/{id}/data', [RajapickerController::class, 'getImageData'])->name('rajapicker.data');
    Route::get('rajapicker/{id}/stream', [RajapickerController::class, 'streamImage'])->name('rajapicker.stream');
    Route::get('rajapicker/{id}/download', [RajapickerController::class, 'downloadImage'])->name('rajapicker.download');
});

// -----------------------------------------------
// Dynamic Image Render (tanpa prefix)
// -----------------------------------------------

Route::get('/gambar', [GambarController::class, 'render'])
    ->name('gambar.render')
    ->withoutMiddleware(['web']);


