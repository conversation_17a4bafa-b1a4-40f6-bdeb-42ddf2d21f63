<?php

namespace Modules\Rajapicker\Traits;

use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

/**
 * Trait SimpanMedia
 * 
 * Trait untuk menyimpan gambar dari FileUpload ke Spatie Media Library
 * dengan nama field upload yang dinamis
 * 
 * @package Modules\Rajapicker\Traits
 */
trait SimpanMedia
{
    /**
     * Simpan file upload ke media library
     * 
     * @param string|array $fieldValue Nilai field upload (path file atau array path)
     * @param string $fieldName Nama field upload
     * @param string $collection Nama collection media (default: 'default')
     * @param array $options Opsi tambahan untuk penyimpanan
     * @return Media|array|null Media item yang dibuat atau array media items
     */
    public function simpanMedia($fieldValue, string $fieldName, string $collection = 'default', array $options = [])
    {
        if (empty($fieldValue)) {
            return null;
        }

        // Jika multiple files (array)
        if (is_array($fieldValue)) {
            return $this->simpanMultipleMedia($fieldValue, $fieldName, $collection, $options);
        }

        // Single file
        return $this->simpanSingleMedia($fieldValue, $fieldName, $collection, $options);
    }

    /**
     * Simpan single file ke media library
     * 
     * @param string $filePath Path file yang akan disimpan
     * @param string $fieldName Nama field upload
     * @param string $collection Nama collection media
     * @param array $options Opsi tambahan
     * @return Media|null
     */
    protected function simpanSingleMedia(string $filePath, string $fieldName, string $collection, array $options = []): ?Media
    {
        try {
            // Cek apakah file ada di storage
            $fullPath = $this->getFullPath($filePath);
            
            if (!file_exists($fullPath)) {
                Log::error('SimpanMedia: File tidak ditemukan', [
                    'field_name' => $fieldName,
                    'file_path' => $filePath,
                    'full_path' => $fullPath
                ]);
                return null;
            }

            // Ambil nama file asli
            $originalName = $options['original_name'] ?? basename($filePath);
            $fileName = $options['file_name'] ?? basename($filePath);

            // Tambahkan ke media library
            $mediaItem = $this->addMedia($fullPath)
                ->usingName($originalName)
                ->usingFileName($fileName);

            // Set custom properties jika ada
            if (isset($options['custom_properties'])) {
                $mediaItem->withCustomProperties($options['custom_properties']);
            }

            // Simpan ke collection
            $media = $mediaItem->toMediaCollection($collection);

            Log::info('SimpanMedia: File berhasil disimpan', [
                'field_name' => $fieldName,
                'media_id' => $media->id,
                'collection' => $collection,
                'file_name' => $media->file_name
            ]);

            return $media;

        } catch (\Exception $e) {
            Log::error('SimpanMedia: Gagal menyimpan file', [
                'field_name' => $fieldName,
                'file_path' => $filePath,
                'collection' => $collection,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return null;
        }
    }

    /**
     * Simpan multiple files ke media library
     * 
     * @param array $filePaths Array path files
     * @param string $fieldName Nama field upload
     * @param string $collection Nama collection media
     * @param array $options Opsi tambahan
     * @return array Array media items
     */
    protected function simpanMultipleMedia(array $filePaths, string $fieldName, string $collection, array $options = []): array
    {
        $mediaItems = [];

        foreach ($filePaths as $index => $filePath) {
            if (empty($filePath)) {
                continue;
            }

            // Set options untuk file individual
            $fileOptions = $options;
            if (isset($options['original_names'][$index])) {
                $fileOptions['original_name'] = $options['original_names'][$index];
            }
            if (isset($options['file_names'][$index])) {
                $fileOptions['file_name'] = $options['file_names'][$index];
            }

            $media = $this->simpanSingleMedia($filePath, $fieldName, $collection, $fileOptions);
            
            if ($media) {
                $mediaItems[] = $media;
            }
        }

        return $mediaItems;
    }

    /**
     * Simpan file dari UploadedFile object
     * 
     * @param UploadedFile $uploadedFile File yang diupload
     * @param string $fieldName Nama field upload
     * @param string $collection Nama collection media
     * @param array $options Opsi tambahan
     * @return Media|null
     */
    public function simpanMediaDariUpload(UploadedFile $uploadedFile, string $fieldName, string $collection = 'default', array $options = []): ?Media
    {
        try {
            // Ambil nama file asli
            $originalName = $options['original_name'] ?? $uploadedFile->getClientOriginalName();
            $fileName = $options['file_name'] ?? $uploadedFile->getClientOriginalName();

            // Tambahkan ke media library langsung dari UploadedFile
            $mediaItem = $this->addMedia($uploadedFile)
                ->usingName($originalName)
                ->usingFileName($fileName);

            // Set custom properties jika ada
            if (isset($options['custom_properties'])) {
                $mediaItem->withCustomProperties($options['custom_properties']);
            }

            // Simpan ke collection
            $media = $mediaItem->toMediaCollection($collection);

            Log::info('SimpanMedia: UploadedFile berhasil disimpan', [
                'field_name' => $fieldName,
                'media_id' => $media->id,
                'collection' => $collection,
                'original_name' => $originalName
            ]);

            return $media;

        } catch (\Exception $e) {
            Log::error('SimpanMedia: Gagal menyimpan UploadedFile', [
                'field_name' => $fieldName,
                'collection' => $collection,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return null;
        }
    }

    /**
     * Hapus media berdasarkan field name dan collection
     * 
     * @param string $fieldName Nama field
     * @param string $collection Nama collection
     * @return bool
     */
    public function hapusMedia(string $fieldName, string $collection = 'default'): bool
    {
        try {
            $mediaItems = $this->getMedia($collection);
            
            foreach ($mediaItems as $media) {
                $media->delete();
            }

            Log::info('SimpanMedia: Media berhasil dihapus', [
                'field_name' => $fieldName,
                'collection' => $collection,
                'count' => $mediaItems->count()
            ]);

            return true;

        } catch (\Exception $e) {
            Log::error('SimpanMedia: Gagal menghapus media', [
                'field_name' => $fieldName,
                'collection' => $collection,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Ambil full path dari relative path
     * 
     * @param string $relativePath Path relatif
     * @return string Full path
     */
    protected function getFullPath(string $relativePath): string
    {
        // Jika sudah full path, return as is
        if (file_exists($relativePath)) {
            return $relativePath;
        }

        // Coba dari storage public
        $publicPath = Storage::disk('public')->path($relativePath);
        if (file_exists($publicPath)) {
            return $publicPath;
        }

        // Coba dari storage path langsung
        $storagePath = storage_path('app/public/' . $relativePath);
        if (file_exists($storagePath)) {
            return $storagePath;
        }

        // Coba dari livewire temp
        $livewirePath = storage_path('app/livewire-tmp/' . $relativePath);
        if (file_exists($livewirePath)) {
            return $livewirePath;
        }

        // Return original jika tidak ditemukan
        return $relativePath;
    }

    /**
     * Ambil URL media dari field dan collection
     * 
     * @param string $fieldName Nama field
     * @param string $collection Nama collection
     * @param string $conversion Nama conversion (optional)
     * @return string|null
     */
    public function getMediaUrl(string $fieldName, string $collection = 'default', string $conversion = ''): ?string
    {
        return $this->getFirstMediaUrl($collection, $conversion);
    }

    /**
     * Ambil semua URL media dari field dan collection
     * 
     * @param string $fieldName Nama field
     * @param string $collection Nama collection
     * @param string $conversion Nama conversion (optional)
     * @return array
     */
    public function getMediaUrls(string $fieldName, string $collection = 'default', string $conversion = ''): array
    {
        $urls = [];
        $mediaItems = $this->getMedia($collection);
        
        foreach ($mediaItems as $media) {
            $urls[] = $media->getUrl($conversion);
        }
        
        return $urls;
    }
}
