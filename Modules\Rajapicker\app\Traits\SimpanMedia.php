<?php

namespace Modules\Rajapicker\Traits;

use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

/**
 * Trait SimpanMedia
 * 
 * Trait untuk menyimpan gambar dari FileUpload ke Spatie Media Library
 * dengan nama field upload yang dinamis
 * 
 * @package Modules\Rajapicker\Traits
 */
trait SimpanMedia
{
    /**
     * Get configuration value for SimpanMedia
     *
     * @param string $key
     * @param mixed $default
     * @return mixed
     */
    protected function getSimpanMediaConfig(string $key, $default = null)
    {
        return Config::get("rajapicker.simpanmedia.{$key}", $default);
    }

    /**
     * Simpan file upload ke media library
     *
     * @param string|array $fieldValue Nilai field upload (path file atau array path)
     * @param string $fieldName Nama field upload
     * @param string $collection Nama collection media (default: 'default')
     * @param array $options Opsi tambahan untuk penyimpanan
     * @return Media|array|null Media item yang dibuat atau array media items
     */
    public function simpanMedia($fieldValue, string $fieldName, ?string $collection = null, array $options = [])
    {
        if (empty($fieldValue)) {
            return null;
        }

        // Gunakan collection default dari config jika tidak disediakan
        $collection = $collection ?? $this->getSimpanMediaConfig('defaults.collection', 'default');

        // Merge options dengan default config
        $options = array_merge([
            'deleteExisting' => $this->getSimpanMediaConfig('defaults.delete_existing', true),
            'preserveOriginalName' => $this->getSimpanMediaConfig('defaults.preserve_original_name', false),
            'generateUniqueNames' => $this->getSimpanMediaConfig('defaults.generate_unique_names', true),
            'validateFiles' => $this->getSimpanMediaConfig('defaults.validate_files', true),
            'disk' => $this->getSimpanMediaConfig('defaults.disk', 'public'),
        ], $options);

        // Jika multiple files (array)
        if (is_array($fieldValue)) {
            return $this->simpanMultipleMedia($fieldValue, $fieldName, $collection, $options);
        }

        // Single file
        return $this->simpanSingleMedia($fieldValue, $fieldName, $collection, $options);
    }

    /**
     * Simpan single file ke media library
     * 
     * @param string $filePath Path file yang akan disimpan
     * @param string $fieldName Nama field upload
     * @param string $collection Nama collection media
     * @param array $options Opsi tambahan
     * @return Media|null
     */
    protected function simpanSingleMedia(string $filePath, string $fieldName, string $collection, array $options = []): ?Media
    {
        try {
            // Validasi file jika diaktifkan
            if ($options['validateFiles'] && !$this->validateFile($filePath)) {
                return null;
            }

            // Hapus media yang sudah ada jika diaktifkan
            if ($options['deleteExisting']) {
                $this->hapusMedia($fieldName, $collection);
            }

            // Cek apakah file ada di storage
            $fullPath = $this->getFullPath($filePath);

            if (!file_exists($fullPath)) {
                $this->logError('File tidak ditemukan', [
                    'field_name' => $fieldName,
                    'file_path' => $filePath,
                    'full_path' => $fullPath
                ]);
                return null;
            }

            // Generate nama file
            $originalName = $options['original_name'] ?? basename($filePath);
            $fileName = $this->generateFileName($filePath, $options);

            // Tambahkan ke media library
            $mediaItem = $this->addMedia($fullPath)
                ->usingName($originalName)
                ->usingFileName($fileName);

            // Set disk jika disediakan
            if (isset($options['disk'])) {
                $mediaItem->toMediaCollection($collection, $options['disk']);
            }

            // Set custom properties
            $customProperties = $this->getCustomProperties($collection, $options);
            if (!empty($customProperties)) {
                $mediaItem->withCustomProperties($customProperties);
            }

            // Simpan ke collection
            $media = $mediaItem->toMediaCollection($collection);

            $this->logInfo('File berhasil disimpan', [
                'field_name' => $fieldName,
                'media_id' => $media->id,
                'collection' => $collection,
                'file_name' => $media->file_name
            ]);

            return $media;

        } catch (\Exception $e) {
            Log::error('SimpanMedia: Gagal menyimpan file', [
                'field_name' => $fieldName,
                'file_path' => $filePath,
                'collection' => $collection,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return null;
        }
    }

    /**
     * Simpan multiple files ke media library
     * 
     * @param array $filePaths Array path files
     * @param string $fieldName Nama field upload
     * @param string $collection Nama collection media
     * @param array $options Opsi tambahan
     * @return array Array media items
     */
    protected function simpanMultipleMedia(array $filePaths, string $fieldName, string $collection, array $options = []): array
    {
        $mediaItems = [];

        foreach ($filePaths as $index => $filePath) {
            if (empty($filePath)) {
                continue;
            }

            // Set options untuk file individual
            $fileOptions = $options;
            if (isset($options['original_names'][$index])) {
                $fileOptions['original_name'] = $options['original_names'][$index];
            }
            if (isset($options['file_names'][$index])) {
                $fileOptions['file_name'] = $options['file_names'][$index];
            }

            $media = $this->simpanSingleMedia($filePath, $fieldName, $collection, $fileOptions);
            
            if ($media) {
                $mediaItems[] = $media;
            }
        }

        return $mediaItems;
    }

    /**
     * Simpan file dari UploadedFile object
     * 
     * @param UploadedFile $uploadedFile File yang diupload
     * @param string $fieldName Nama field upload
     * @param string $collection Nama collection media
     * @param array $options Opsi tambahan
     * @return Media|null
     */
    public function simpanMediaDariUpload(UploadedFile $uploadedFile, string $fieldName, string $collection = 'default', array $options = []): ?Media
    {
        try {
            // Ambil nama file asli
            $originalName = $options['original_name'] ?? $uploadedFile->getClientOriginalName();
            $fileName = $options['file_name'] ?? $uploadedFile->getClientOriginalName();

            // Tambahkan ke media library langsung dari UploadedFile
            $mediaItem = $this->addMedia($uploadedFile)
                ->usingName($originalName)
                ->usingFileName($fileName);

            // Set custom properties jika ada
            if (isset($options['custom_properties'])) {
                $mediaItem->withCustomProperties($options['custom_properties']);
            }

            // Simpan ke collection
            $media = $mediaItem->toMediaCollection($collection);

            Log::info('SimpanMedia: UploadedFile berhasil disimpan', [
                'field_name' => $fieldName,
                'media_id' => $media->id,
                'collection' => $collection,
                'original_name' => $originalName
            ]);

            return $media;

        } catch (\Exception $e) {
            Log::error('SimpanMedia: Gagal menyimpan UploadedFile', [
                'field_name' => $fieldName,
                'collection' => $collection,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return null;
        }
    }

    /**
     * Hapus media berdasarkan field name dan collection
     * 
     * @param string $fieldName Nama field
     * @param string $collection Nama collection
     * @return bool
     */
    public function hapusMedia(string $fieldName, string $collection = 'default'): bool
    {
        try {
            $mediaItems = $this->getMedia($collection);
            
            foreach ($mediaItems as $media) {
                $media->delete();
            }

            Log::info('SimpanMedia: Media berhasil dihapus', [
                'field_name' => $fieldName,
                'collection' => $collection,
                'count' => $mediaItems->count()
            ]);

            return true;

        } catch (\Exception $e) {
            Log::error('SimpanMedia: Gagal menghapus media', [
                'field_name' => $fieldName,
                'collection' => $collection,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Ambil full path dari relative path
     * 
     * @param string $relativePath Path relatif
     * @return string Full path
     */
    protected function getFullPath(string $relativePath): string
    {
        // Jika sudah full path, return as is
        if (file_exists($relativePath)) {
            return $relativePath;
        }

        // Coba dari storage public
        $publicPath = Storage::disk('public')->path($relativePath);
        if (file_exists($publicPath)) {
            return $publicPath;
        }

        // Coba dari storage path langsung
        $storagePath = storage_path('app/public/' . $relativePath);
        if (file_exists($storagePath)) {
            return $storagePath;
        }

        // Coba dari livewire temp
        $livewirePath = storage_path('app/livewire-tmp/' . $relativePath);
        if (file_exists($livewirePath)) {
            return $livewirePath;
        }

        // Return original jika tidak ditemukan
        return $relativePath;
    }

    /**
     * Validasi file sebelum upload
     *
     * @param string $filePath
     * @return bool
     */
    protected function validateFile(string $filePath): bool
    {
        $fullPath = $this->getFullPath($filePath);

        if (!file_exists($fullPath)) {
            $this->logError('File tidak ditemukan untuk validasi', ['file_path' => $filePath]);
            return false;
        }

        // Validasi ukuran file
        $fileSize = filesize($fullPath);
        $maxSize = $this->getSimpanMediaConfig('validation.max_file_size', 10485760);
        $minSize = $this->getSimpanMediaConfig('validation.min_file_size', 1024);

        if ($fileSize > $maxSize) {
            $this->logError('File terlalu besar', [
                'file_path' => $filePath,
                'file_size' => $fileSize,
                'max_size' => $maxSize
            ]);
            return false;
        }

        if ($fileSize < $minSize) {
            $this->logError('File terlalu kecil', [
                'file_path' => $filePath,
                'file_size' => $fileSize,
                'min_size' => $minSize
            ]);
            return false;
        }

        // Validasi MIME type
        $mimeType = mime_content_type($fullPath);
        $allowedMimes = $this->getSimpanMediaConfig('validation.allowed_mime_types', []);

        if (!empty($allowedMimes) && !in_array($mimeType, $allowedMimes)) {
            $this->logError('MIME type tidak diizinkan', [
                'file_path' => $filePath,
                'mime_type' => $mimeType,
                'allowed_mimes' => $allowedMimes
            ]);
            return false;
        }

        // Validasi ekstensi
        $extension = strtolower(pathinfo($fullPath, PATHINFO_EXTENSION));
        $allowedExtensions = $this->getSimpanMediaConfig('validation.allowed_extensions', []);

        if (!empty($allowedExtensions) && !in_array($extension, $allowedExtensions)) {
            $this->logError('Ekstensi file tidak diizinkan', [
                'file_path' => $filePath,
                'extension' => $extension,
                'allowed_extensions' => $allowedExtensions
            ]);
            return false;
        }

        return true;
    }

    /**
     * Generate nama file berdasarkan konfigurasi
     *
     * @param string $filePath
     * @param array $options
     * @return string
     */
    protected function generateFileName(string $filePath, array $options): string
    {
        $originalName = basename($filePath);
        $extension = pathinfo($originalName, PATHINFO_EXTENSION);
        $nameWithoutExt = pathinfo($originalName, PATHINFO_FILENAME);

        // Jika preserve original name
        if ($options['preserveOriginalName']) {
            return $originalName;
        }

        // Jika menggunakan ULID
        if ($this->getSimpanMediaConfig('file_naming.use_ulid', true)) {
            $fileName = Str::ulid()->toString();
        } else {
            // Gunakan pattern custom
            $pattern = $this->getSimpanMediaConfig('file_naming.custom_pattern', '{ulid}');
            $fileName = str_replace([
                '{ulid}',
                '{original}',
                '{timestamp}',
                '{random}'
            ], [
                Str::ulid()->toString(),
                $nameWithoutExt,
                time(),
                Str::random(8)
            ], $pattern);
        }

        // Tambahkan ekstensi jika preserve extension
        if ($this->getSimpanMediaConfig('file_naming.preserve_extension', true)) {
            $fileName .= '.' . $extension;
        }

        // Convert to lowercase jika diaktifkan
        if ($this->getSimpanMediaConfig('file_naming.lowercase', true)) {
            $fileName = strtolower($fileName);
        }

        return $fileName;
    }

    /**
     * Get custom properties untuk media
     *
     * @param string $collection
     * @param array $options
     * @return array
     */
    protected function getCustomProperties(string $collection, array $options): array
    {
        $properties = [];

        // Default properties
        $defaultProperties = $this->getSimpanMediaConfig('custom_properties.default', []);
        $properties = array_merge($properties, $defaultProperties);

        // Properties berdasarkan collection
        $collectionProperties = $this->getSimpanMediaConfig("custom_properties.by_collection.{$collection}", []);
        $properties = array_merge($properties, $collectionProperties);

        // Properties dari options
        if (isset($options['custom_properties'])) {
            $properties = array_merge($properties, $options['custom_properties']);
        }

        // Tambahkan user ID jika tersedia
        if (Auth::check()) {
            $properties['uploaded_by'] = Auth::id();
        }

        return $properties;
    }

    /**
     * Log error dengan konfigurasi
     *
     * @param string $message
     * @param array $context
     */
    protected function logError(string $message, array $context = []): void
    {
        if ($this->getSimpanMediaConfig('error_handling.log_errors', true)) {
            Log::error("SimpanMedia: {$message}", $context);
        }
    }

    /**
     * Log info dengan konfigurasi
     *
     * @param string $message
     * @param array $context
     */
    protected function logInfo(string $message, array $context = []): void
    {
        if ($this->getSimpanMediaConfig('defaults.enable_logging', true)) {
            Log::info("SimpanMedia: {$message}", $context);
        }
    }

    /**
     * Ambil URL media dari field dan collection
     * 
     * @param string $fieldName Nama field
     * @param string $collection Nama collection
     * @param string $conversion Nama conversion (optional)
     * @return string|null
     */
    public function getMediaUrl(string $fieldName, string $collection = 'default', string $conversion = ''): ?string
    {
        return $this->getFirstMediaUrl($collection, $conversion);
    }

    /**
     * Ambil semua URL media dari field dan collection
     * 
     * @param string $fieldName Nama field
     * @param string $collection Nama collection
     * @param string $conversion Nama conversion (optional)
     * @return array
     */
    public function getMediaUrls(string $fieldName, string $collection = 'default', string $conversion = ''): array
    {
        $urls = [];
        $mediaItems = $this->getMedia($collection);
        
        foreach ($mediaItems as $media) {
            $urls[] = $media->getUrl($conversion);
        }
        
        return $urls;
    }
}
