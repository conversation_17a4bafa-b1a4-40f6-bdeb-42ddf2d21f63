<?php

namespace Modules\Rajapicker\Filament\Forms\Components;

use Filament\Forms\Components\FileUpload;
use Modules\Rajapicker\Services\RajaPickerConfigService;
use Modules\Rajapicker\Services\RajaPickerThumbnailService;
use Modules\Rajapicker\Services\RajaUploadConfigService;

use Livewire\Features\SupportFileUploads\TemporaryUploadedFile;

class RajaUpload extends FileUpload
{
    protected ?RajaPickerConfigService $configService = null;
    protected ?RajaPickerThumbnailService $thumbnailService = null;
    protected ?RajaUploadConfigService $rajaUploadConfig = null;
    
    // Konfigurasi khusus RajaUpload
    protected string $rajaCollection = 'default';
    protected bool $generateThumbnails = true;
    protected bool $convertToWebp = false;
    protected bool $preserveOriginal = true;
    protected bool $autoSetUserId = true;
    protected array $thumbnailSizes = [];
    
    protected function setUp(): void
    {
        parent::setUp();

        // Debug logging untuk memastikan setUp dipanggil
        $debugLog = storage_path('debug_rajaupload_setup.txt');
        file_put_contents($debugLog,
            date('Y-m-d H:i:s') . " - RajaUpload setUp() CALLED\n" .
            "Component class: " . get_class($this) . "\n" .
            "Collection: " . $this->rajaCollection . "\n\n",
            FILE_APPEND
        );

        // Load konfigurasi default dari config
        $this->loadDefaultConfiguration();
        
        // Set default disk dan directory
        $this->disk('public');
        $this->directory('uploads/' . $this->rajaCollection);
        
        // Set default accepted file types untuk image
        $this->acceptedFileTypes($this->getConfigService()->getFieldConfig($this->rajaCollection)['accepted_file_types'] ?? ['image/*']);
        
        // Set default max file size
        $maxSize = $this->getConfigService()->getFieldConfig($this->rajaCollection)['max_file_size'] ?? 10;
        $this->maxSize($maxSize * 1024); // Convert MB to KB
        
        // Set default image settings
        $this->image();
        $this->imagePreviewHeight('250');
        $this->loadingIndicatorPosition('center');
        $this->panelLayout('integrated');
        
        // Enable image editor by default
        $this->imageEditor();
        $this->imageEditorAspectRatios([
            null, // free crop
            '16:9',
            '4:3', 
            '1:1',
        ]);
        
        // Custom file name generation dinonaktifkan sementara untuk debugging
        // $this->getUploadedFileNameForStorageUsing(
        //     fn (TemporaryUploadedFile $file): string => $this->generateFileName($file)
        // );
        
        // Set storage path untuk file upload
        $storagePath = 'uploads';
        $collectionPath = $this->rajaCollection;
        $this->directory($storagePath . '/' . $collectionPath);

        // Debug logging FINAL - setelah semua konfigurasi
        $debugLog = storage_path('debug_rajaupload_setup.txt');
        file_put_contents($debugLog,
            date('Y-m-d H:i:s') . " - FINAL setUp() - Collection: " . $this->rajaCollection . "\n" .
            "Directory: " . $this->getDirectory() . "\n\n",
            FILE_APPEND
        );

        // HOOK DIPINDAH KE METHOD collection() agar collection sudah benar

        // Set up media library integration (logging only)
        $this->setupMediaLibraryIntegration();
    }
    
    /**
     * Load konfigurasi default dari config file
     */
    protected function loadDefaultConfiguration(): void
    {
        $rajaConfig = $this->getRajaUploadConfig();

        // Set default storage disk
        $this->disk($rajaConfig->getStorageDisk());

        // Set default max file size
        $this->maxSize($rajaConfig->getMaxFileSize());

        // Set default max files
        $this->maxFiles($rajaConfig->getMaxFiles());

        // Set WebP conversion
        $this->convertToWebp = $rajaConfig->isWebpEnabled();

        // Set thumbnail generation
        $this->generateThumbnails = $rajaConfig->isThumbnailsEnabled();

        // Set thumbnail sizes
        if ($rajaConfig->isThumbnailsEnabled()) {
            $this->thumbnailSizes = $rajaConfig->getThumbnailSizeNames();
        }
    }
    
    /**
     * Set collection untuk upload
     */
    public function collection(string $collection): static
    {
        // Debug logging untuk method collection
        $debugLog = storage_path('debug_rajaupload_setup.txt');
        file_put_contents($debugLog,
            date('Y-m-d H:i:s') . " - collection() method CALLED with: $collection\n" .
            "Previous collection: " . $this->rajaCollection . "\n\n",
            FILE_APPEND
        );

        $this->rajaCollection = $collection;

        $rajaConfig = $this->getRajaUploadConfig();

        // Update directory berdasarkan collection
        $basePath = $rajaConfig->getBasePath();
        $collectionDir = $rajaConfig->getCollectionDirectory($collection);
        $this->directory($basePath . '/' . $collectionDir);

        // Update konfigurasi berdasarkan collection
        $collectionConfig = $rajaConfig->getCollectionConfig($collection);

        if ($collectionConfig) {
            // Set max files
            if (isset($collectionConfig['max_files'])) {
                $this->maxFiles($collectionConfig['max_files']);
            }

            // Set file types
            if (isset($collectionConfig['file_types'])) {
                foreach ($collectionConfig['file_types'] as $fileType) {
                    $typeConfig = $rajaConfig->getFileTypeConfig($fileType);
                    if ($typeConfig) {
                        $this->acceptedFileTypes($typeConfig['mime_types'] ?? []);
                        $this->maxSize($typeConfig['max_size'] ?? $rajaConfig->getMaxFileSize());
                    }
                }
            }
        }

        // Setup hook SETELAH collection dikonfigurasi dengan benar
        $this->setupSaveUploadedFileHook();

        return $this;
    }
    
    /**
     * Setup hook saveUploadedFileUsing dengan collection yang benar
     */
    protected function setupSaveUploadedFileHook(): void
    {
        // Hook saveUploadedFileUsing - UNIFIED HOOK untuk semua fungsi
        $this->saveUploadedFileUsing(function (TemporaryUploadedFile $file, $component) {
            // Debug logging alternatif - file based
            $debugLog = storage_path('debug_rajaupload_hook.txt');
            file_put_contents($debugLog,
                date('Y-m-d H:i:s') . " - UNIFIED Hook saveUploadedFileUsing EXECUTED\n" .
                "Original name: " . $file->getClientOriginalName() . "\n" .
                "Temp path: " . $file->getRealPath() . "\n" .
                "Component directory: " . $component->getDirectory() . "\n" .
                "Component disk: " . $component->getDiskName() . "\n" .
                "Component collection: " . $component->rajaCollection . "\n\n",
                FILE_APPEND
            );

            \Illuminate\Support\Facades\Log::info('RajaUpload: UNIFIED saveUploadedFileUsing hook called', [
                'original_name' => $file->getClientOriginalName(),
                'temp_path' => $file->getRealPath(),
                'component_directory' => $component->getDirectory(),
                'component_disk' => $component->getDiskName()
            ]);

            // Simpan file menggunakan parent method terlebih dahulu
            $filename = $component->getUploadedFileNameForStorage($file);
            $path = $file->storeAs($component->getDirectory(), $filename, $component->getDiskName());

            file_put_contents($debugLog,
                date('Y-m-d H:i:s') . " - File stored successfully\n" .
                "Stored path: " . $path . "\n" .
                "Filename: " . $filename . "\n" .
                "Full storage path: " . storage_path('app/public/' . $path) . "\n\n",
                FILE_APPEND
            );

            \Illuminate\Support\Facades\Log::info('RajaUpload: File stored at path', [
                'stored_path' => $path,
                'filename' => $filename,
                'full_storage_path' => storage_path('app/public/' . $path)
            ]);

            // Debug: Cek apakah method saveToMediaLibrary dipanggil
            file_put_contents($debugLog,
                date('Y-m-d H:i:s') . " - CALLING saveToMediaLibrary method\n",
                FILE_APPEND
            );

            // Simpan ke media library jika model mendukung HasMedia
            $this->saveToMediaLibrary($file, $path, $filename);

            file_put_contents($debugLog,
                date('Y-m-d H:i:s') . " - saveToMediaLibrary method COMPLETED\n",
                FILE_APPEND
            );

            return $path;
        });
    }

    /**
     * Enable/disable thumbnail generation
     */
    public function generateThumbnails(bool $generate = true): static
    {
        $this->generateThumbnails = $generate;
        return $this;
    }
    
    /**
     * Enable/disable WebP conversion
     */
    public function convertToWebp(bool $convert = true): static
    {
        $this->convertToWebp = $convert;
        return $this;
    }
    
    /**
     * Set custom thumbnail sizes
     */
    public function thumbnailSizes(array $sizes): static
    {
        $this->thumbnailSizes = $sizes;
        return $this;
    }
    
    /**
     * Enable/disable auto set user_id
     */
    public function autoSetUserId(bool $auto = true): static
    {
        $this->autoSetUserId = $auto;
        return $this;
    }
    
    /**
     * Generate custom file name
     */
    protected function generateFileName(TemporaryUploadedFile $file): string
    {
        $rajaConfig = $this->getRajaUploadConfig();

        // Get collection-specific naming pattern or use default
        $collectionConfig = $rajaConfig->getCollectionConfig($this->rajaCollection);
        $pattern = $collectionConfig['naming_pattern'] ?? $rajaConfig->getNamingPattern();

        $originalName = $file->getClientOriginalName();

        // Generate filename using RajaUploadConfigService
        $userId = 'guest';
        try {
            $userId = \Illuminate\Support\Facades\Auth::id() ?? 'guest';
        } catch (\Exception) {
            // Fallback to guest if auth fails
        }

        return $rajaConfig->generateFilename($originalName, $pattern, [
            'collection' => $this->rajaCollection,
            'user_id' => $userId,
        ]);
    }

    /**
     * Setup media library integration
     */
    protected function setupMediaLibraryIntegration(): void
    {
        \Illuminate\Support\Facades\Log::info('RajaUpload: Media library integration setup - hook already configured in setUp()');
        // Hook sudah dikonfigurasi di setUp() method sebagai unified hook
        // Tidak perlu hook tambahan di sini
    }

    /**
     * Add file to media library after successful save by FilamentPHP
     */
    protected function addFileToMediaLibraryAfterSave(string $storedPath): void
    {
        // Debug logging alternatif - file based
        $debugLog = storage_path('debug_rajaupload_hook.txt');
        file_put_contents($debugLog,
            date('Y-m-d H:i:s') . " - Method addFileToMediaLibraryAfterSave CALLED\n" .
            "Stored path: " . $storedPath . "\n",
            FILE_APPEND
        );

        try {
            \Illuminate\Support\Facades\Log::info('RajaUpload: addFileToMediaLibraryAfterSave called', [
                'stored_path' => $storedPath
            ]);

            // Get model record
            $record = $this->getModelRecord();

            file_put_contents($debugLog,
                date('Y-m-d H:i:s') . " - Record retrieval result: " . ($record ? 'FOUND' : 'NOT FOUND') . "\n",
                FILE_APPEND
            );

            if (!$record) {
                \Illuminate\Support\Facades\Log::warning('RajaUpload: No model record found in addFileToMediaLibraryAfterSave');
                return;
            }

            // Construct full file path
            $fullPath = storage_path( $storedPath);

            \Illuminate\Support\Facades\Log::info('RajaUpload: Checking file existence', [
                'full_path' => $fullPath,
                'file_exists' => file_exists($fullPath)
            ]);

            if (!file_exists($fullPath)) {
                \Illuminate\Support\Facades\Log::error('RajaUpload: File not found after save', [
                    'full_path' => $fullPath,
                    'stored_path' => $storedPath
                ]);
                return;
            }

            // Extract filename from stored path
            $filename = basename($storedPath);
            $originalName = $filename;

            // Add to media library
            $mediaItem = $record->addMedia($fullPath)
                ->usingName($originalName)
                ->usingFileName($filename)
                ->toMediaCollection($this->rajaCollection);

            \Illuminate\Support\Facades\Log::info('RajaUpload: Media item created successfully', [
                'media_id' => $mediaItem->id,
                'media_name' => $mediaItem->name,
                'collection' => $mediaItem->collection_name,
                'file_name' => $mediaItem->file_name,
                'size' => $mediaItem->size
            ]);

        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('RajaUpload: Failed in addFileToMediaLibraryAfterSave', [
                'error' => $e->getMessage(),
                'stored_path' => $storedPath,
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * Save state to media library (alternative approach)
     */
    protected function saveStateToMediaLibrary($state): void
    {
        try {
            \Illuminate\Support\Facades\Log::info('RajaUpload: saveStateToMediaLibrary called', [
                'state' => $state,
                'state_type' => gettype($state)
            ]);

            if (!$state) {
                return;
            }

            // Get model record
            $record = $this->getModelRecord();
            if (!$record) {
                \Illuminate\Support\Facades\Log::warning('RajaUpload: No model record found in saveStateToMediaLibrary');
                return;
            }

            // Handle array of files or single file
            $files = is_array($state) ? $state : [$state];

            foreach ($files as $filePath) {
                if (is_string($filePath)) {
                    $this->addFileToMediaLibrary($record, $filePath);
                }
            }

        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('RajaUpload: Failed in saveStateToMediaLibrary', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * Add file to media library from file path
     */
    protected function addFileToMediaLibrary($record, string $filePath): void
    {
        try {
            // Try different possible paths - prioritize the correct storage location
            $possiblePaths = [
                storage_path( $filePath),    // Storage app/public path (most likely for FilamentPHP)
                storage_path( $filePath),           // Storage app path
                storage_path($filePath),                    // Direct storage path
                public_path( $filePath),        // Public storage path
            ];

            // Also try livewire-tmp paths for newly uploaded files
            $filename = basename($filePath);

            // Extract original filename from the processed filename
            // Format: YYYYMMDDHHMMSS_randomid_originalname.ext
            $originalFilename = null;
            if (preg_match('/^\d{14}_[a-z0-9]+_(.+)$/', $filename, $matches)) {
                $originalFilename = $matches[1];
            }

            // Try to find files in livewire-tmp with various patterns
            $livewireTmpPatterns = [
                storage_path('livewire-tmp/*/' . $filename),
            ];

            // If we have original filename, search for it in livewire-tmp
            if ($originalFilename) {
                $livewireTmpPatterns[] = storage_path('livewire-tmp/rajapicker_*.' . pathinfo($originalFilename, PATHINFO_EXTENSION));

                // Also try to find by original filename pattern
                $baseOriginalName = pathinfo($originalFilename, PATHINFO_FILENAME);
                $livewireTmpPatterns[] = storage_path('livewire-tmp/*/' . $baseOriginalName . '*');
            }

            foreach ($livewireTmpPatterns as $pattern) {
                $foundFiles = glob($pattern);
                if (!empty($foundFiles)) {
                    // Sort by modification time, get the most recent
                    usort($foundFiles, function($a, $b) {
                        return filemtime($b) - filemtime($a);
                    });

                    // Filter files to only include recent ones (within last 10 minutes)
                    $recentFiles = array_filter($foundFiles, function($file) {
                        return (time() - filemtime($file)) < 600; // 10 minutes
                    });

                    if (!empty($recentFiles)) {
                        // Add the most recent files to possible paths
                        $possiblePaths = array_merge(array_slice($recentFiles, 0, 2), $possiblePaths);
                        break;
                    }
                }
            }

            $fullPath = null;
            foreach ($possiblePaths as $path) {
                if (file_exists($path)) {
                    $fullPath = $path;
                    \Illuminate\Support\Facades\Log::info('RajaUpload: Found file at path', [
                        'found_path' => $path
                    ]);
                    break;
                }
            }

            \Illuminate\Support\Facades\Log::info('RajaUpload: Adding file to media library', [
                'file_path' => $filePath,
                'full_path' => $fullPath,
                'file_exists' => $fullPath !== null,
                'tried_paths' => $possiblePaths
            ]);

            if (!$fullPath) {
                \Illuminate\Support\Facades\Log::warning('RajaUpload: File not found for media library', [
                    'tried_paths' => $possiblePaths
                ]);
                return;
            }

            // Extract filename from path
            $filename = basename($filePath);
            $originalName = $filename;

            // Add to media library
            $mediaItem = $record->addMedia($fullPath)
                ->usingName($originalName)
                ->usingFileName($filename)
                ->toMediaCollection($this->rajaCollection);

            \Illuminate\Support\Facades\Log::info('RajaUpload: Media item created from state', [
                'media_id' => $mediaItem->id,
                'media_name' => $mediaItem->name,
                'collection' => $mediaItem->collection_name
            ]);

        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('RajaUpload: Failed to add file to media library from state', [
                'error' => $e->getMessage(),
                'file_path' => $filePath,
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * Save uploaded file to media library
     */
    protected function saveToMediaLibrary(TemporaryUploadedFile $file, string $storedPath, string $filename): void
    {
        try {
            // Debug logging
            \Illuminate\Support\Facades\Log::info('RajaUpload: Starting media library integration', [
                'filename' => $filename,
                'stored_path' => $storedPath,
                'collection' => $this->rajaCollection
            ]);

            // Dapatkan model dari form context
            $record = $this->getModelRecord();

            if (!$record) {
                \Illuminate\Support\Facades\Log::warning('RajaUpload: No model record found');
                return;
            }

            \Illuminate\Support\Facades\Log::info('RajaUpload: Model record found', [
                'model_class' => get_class($record),
                'model_id' => $record->id ?? 'no-id'
            ]);

            if (!method_exists($record, 'addMedia')) {
                \Illuminate\Support\Facades\Log::warning('RajaUpload: Model does not support media library', [
                    'model_class' => get_class($record)
                ]);
                return;
            }

            // Dapatkan full path file yang sudah disimpan - sesuaikan dengan disk public
            $fullPath = storage_path('app/public/' . $storedPath);

            \Illuminate\Support\Facades\Log::info('RajaUpload: Checking file path', [
                'full_path' => $fullPath,
                'file_exists' => file_exists($fullPath)
            ]);

            if (!file_exists($fullPath)) {
                \Illuminate\Support\Facades\Log::warning('RajaUpload: File not found at path', [
                    'full_path' => $fullPath
                ]);
                return;
            }

            \Illuminate\Support\Facades\Log::info('RajaUpload: Using addMediaFromDisk approach', [
                'disk' => 'public',
                'path' => $storedPath
            ]);

            // ALTERNATIVE APPROACH: Gunakan addMediaFromDisk untuk menghindari disk access issue
            $mediaItem = $record->addMediaFromDisk($storedPath, 'public')
                ->usingName($file->getClientOriginalName())
                ->usingFileName($filename)
                ->toMediaCollection($this->rajaCollection);

            \Illuminate\Support\Facades\Log::info('RajaUpload: Media item created successfully', [
                'media_id' => $mediaItem->id,
                'media_name' => $mediaItem->name,
                'collection' => $mediaItem->collection_name
            ]);

            // Set custom properties dari config
            $rajaConfig = $this->getRajaUploadConfig();
            $collectionConfig = $rajaConfig->getCollectionConfig($this->rajaCollection);

            if ($collectionConfig && isset($collectionConfig['custom_properties'])) {
                $mediaItem->setCustomProperty('raja_config', $collectionConfig);
                $mediaItem->save();
                \Illuminate\Support\Facades\Log::info('RajaUpload: Custom properties set');
            }

        } catch (\Exception $e) {
            // Log error tapi jangan gagalkan upload
            \Illuminate\Support\Facades\Log::error('RajaUpload: Failed to save to media library', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'file' => $filename,
                'collection' => $this->rajaCollection
            ]);
        }
    }

    /**
     * Get model record from form context
     */
    protected function getModelRecord()
    {
        $livewire = $this->getLivewire();

        // Try different ways to get the record
        if (method_exists($livewire, 'getRecord')) {
            return $livewire->getRecord();
        }

        if (property_exists($livewire, 'record')) {
            return $livewire->record;
        }

        if (method_exists($livewire, 'getModel')) {
            return $livewire->getModel();
        }

        return null;
    }


    
    /**
     * Get config service instance
     */
    protected function getConfigService(): RajaPickerConfigService
    {
        if ($this->configService === null) {
            $this->configService = new RajaPickerConfigService();
        }
        
        return $this->configService;
    }
    
    /**
     * Get thumbnail service instance
     */
    protected function getThumbnailService(): RajaPickerThumbnailService
    {
        if ($this->thumbnailService === null) {
            $this->thumbnailService = new RajaPickerThumbnailService();
        }

        return $this->thumbnailService;
    }

    /**
     * Get RajaUpload config service instance
     */
    protected function getRajaUploadConfig(): RajaUploadConfigService
    {
        if ($this->rajaUploadConfig === null) {
            $this->rajaUploadConfig = new RajaUploadConfigService();
        }

        return $this->rajaUploadConfig;
    }

    /**
     * Get collection name
     */
    public function getCollection(): string
    {
        return $this->rajaCollection;
    }

    /**
     * Load configuration from collection preset
     */
    protected function loadCollectionConfig(string $collection): static
    {
        $config = $this->getRajaUploadConfig();
        $collectionConfig = $config->getCollectionConfig($collection);

        if (!$collectionConfig) {
            return $this;
        }

        // Set collection
        $this->collection($collection);

        // Set max files
        if (isset($collectionConfig['max_files'])) {
            $this->multiple($collectionConfig['max_files'] > 1);
            if ($collectionConfig['max_files'] > 1) {
                $this->maxFiles($collectionConfig['max_files']);
            }
        }

        // Set file types
        if (isset($collectionConfig['file_types'])) {
            foreach ($collectionConfig['file_types'] as $fileType) {
                $typeConfig = $config->getFileTypeConfig($fileType);
                if ($typeConfig) {
                    $this->acceptedFileTypes($typeConfig['mime_types'] ?? []);
                    $this->maxSize($typeConfig['max_size'] ?? $config->getMaxFileSize());
                }
            }
        }

        // Set thumbnails
        if (isset($collectionConfig['thumbnails']) && $collectionConfig['thumbnails']) {
            $this->generateThumbnails(true);
            if (is_array($collectionConfig['thumbnails'])) {
                $this->thumbnailSizes = $collectionConfig['thumbnails'];
            }
        }

        // Set WebP
        if (isset($collectionConfig['webp'])) {
            $this->convertToWebp($collectionConfig['webp']);
        }

        // Set reorderable
        if (isset($collectionConfig['reorderable']) && $collectionConfig['reorderable']) {
            $this->reorderable();
        }

        // Set resize configuration
        if (isset($collectionConfig['resize'])) {
            $resize = $collectionConfig['resize'];
            if (isset($resize['width']) && isset($resize['height'])) {
                $this->imageResizeTargetWidth($resize['width']);
                $this->imageResizeTargetHeight($resize['height']);

                if (isset($resize['fit'])) {
                    switch ($resize['fit']) {
                        case 'crop':
                            $this->imageResizeMode('crop');
                            break;
                        case 'contain':
                            $this->imageResizeMode('contain');
                            break;
                        case 'fill':
                            $this->imageResizeMode('fill');
                            break;
                    }
                }
            }
        }

        return $this;
    }
    
    /**
     * Check if thumbnails should be generated
     */
    public function shouldGenerateThumbnails(): bool
    {
        return $this->generateThumbnails;
    }
    
    /**
     * Check if files should be converted to WebP
     */
    public function shouldConvertToWebp(): bool
    {
        return $this->convertToWebp;
    }

    /**
     * Set avatar mode with circle cropper
     */
    public function avatar(): static
    {
        parent::avatar();
        $this->circleCropper();
        return $this;
    }

    /**
     * Set image resize mode with common presets
     */
    public function imageResizePreset(string $preset = 'medium'): static
    {
        $presets = [
            'small' => ['width' => 800, 'height' => 600],
            'medium' => ['width' => 1200, 'height' => 900],
            'large' => ['width' => 1920, 'height' => 1080],
            'hd' => ['width' => 1920, 'height' => 1080],
            '4k' => ['width' => 3840, 'height' => 2160],
        ];

        if (isset($presets[$preset])) {
            $this->imageResizeMode('cover')
                 ->imageResizeTargetWidth($presets[$preset]['width'])
                 ->imageResizeTargetHeight($presets[$preset]['height']);
        }

        return $this;
    }

    /**
     * Set common aspect ratios for image editor
     */
    public function commonAspectRatios(): static
    {
        $this->imageEditorAspectRatios([
            null, // free crop
            '16:9', // widescreen
            '4:3',  // standard
            '3:2',  // photo
            '1:1',  // square
            '9:16', // portrait/story
        ]);

        return $this;
    }

    /**
     * Set social media optimized aspect ratios
     */
    public function socialMediaAspectRatios(): static
    {
        $this->imageEditorAspectRatios([
            '1:1',   // Instagram post
            '16:9',  // Facebook cover, YouTube thumbnail
            '9:16',  // Instagram/TikTok story
            '4:5',   // Instagram portrait
            '1.91:1', // Facebook post
        ]);

        return $this;
    }

    /**
     * Enable multiple upload with reorderable
     */
    public function gallery(int $maxFiles = 10): static
    {
        $this->multiple()
             ->reorderable()
             ->appendFiles()
             ->maxFiles($maxFiles)
             ->panelLayout('grid');

        return $this;
    }

    /**
     * Set as document upload (non-image files)
     */
    public function document(?array $acceptedTypes = null): static
    {
        $defaultTypes = [
            'application/pdf',
            'application/msword',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'application/vnd.ms-excel',
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'text/plain',
        ];

        $this->acceptedFileTypes($acceptedTypes ?? $defaultTypes);
        $this->previewable(false);
        $this->downloadable();

        return $this;
    }

    /**
     * Quick setup for product images
     */
    public function productImage(): static
    {
        $this->collection('produk')
             ->imageResizePreset('medium')
             ->imageEditor()
             ->commonAspectRatios()
             ->generateThumbnails()
             ->convertToWebp();

        return $this;
    }

    /**
     * Quick setup for gallery images
     */
    public function galleryImages(int $maxFiles = 20): static
    {
        $this->collection('gallery')
             ->gallery($maxFiles)
             ->imageResizePreset('large')
             ->generateThumbnails()
             ->convertToWebp();

        return $this;
    }

    /**
     * Quick setup for banner/hero images
     */
    public function bannerImage(): static
    {
        $this->collection('lainlain')
             ->imageResizePreset('hd')
             ->imageEditor()
             ->imageEditorAspectRatios(['16:9', '21:9', '2:1'])
             ->generateThumbnails()
             ->convertToWebp();

        return $this;
    }

    /**
     * Quick setup for user avatar
     */
    public function userAvatar(): static
    {
        $this->collection('avatars')
             ->avatar()
             ->imageResizePreset('small')
             ->maxSize(2048) // 2MB max for avatars
             ->generateThumbnails()
             ->convertToWebp();

        return $this;
    }

    // ========================================
    // CONFIG-BASED PRESET METHODS
    // ========================================

    /**
     * Basic upload using config
     */
    public function basicUpload(): static
    {
        return $this->loadCollectionConfig('basic');
    }

    /**
     * Product images using config
     */
    public function productImages(): static
    {
        return $this->loadCollectionConfig('products');
    }

    /**
     * Gallery images using config
     */
    public function galleryImagesConfig(): static
    {
        return $this->loadCollectionConfig('gallery');
    }

    /**
     * User avatars using config
     */
    public function userAvatarConfig(): static
    {
        return $this->loadCollectionConfig('avatars');
    }

    /**
     * Document upload using config
     */
    public function documentUpload(): static
    {
        return $this->loadCollectionConfig('documents');
    }

    /**
     * Load any collection from config
     */
    public function fromConfig(string $collection): static
    {
        return $this->loadCollectionConfig($collection);
    }
}
