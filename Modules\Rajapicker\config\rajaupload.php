<?php

return [
    /*
    |--------------------------------------------------------------------------
    | RajaUpload Configuration
    |--------------------------------------------------------------------------
    |
    | Konfigurasi untuk RajaUpload custom field component yang extends
    | FilamentPHP FileUpload dengan fitur tambahan seperti thumbnail
    | generation, WebP conversion, dan custom file naming.
    |
    */

    /*
    |--------------------------------------------------------------------------
    | Storage Configuration
    |--------------------------------------------------------------------------
    */
    'storage' => [
        'disk' => env('RAJAUPLOAD_DISK', 'public'),
        'base_path' => env('RAJAUPLOAD_BASE_PATH', 'uploads'),
        'url_prefix' => env('RAJAUPLOAD_URL_PREFIX', '/storage'),
    ],

    /*
    |--------------------------------------------------------------------------
    | File Upload Settings
    |--------------------------------------------------------------------------
    */
    'upload' => [
        'max_file_size' => env('RAJAUPLOAD_MAX_FILE_SIZE', 10240), // KB (10MB default)
        'max_files' => env('RAJAUPLOAD_MAX_FILES', 10),
        'chunk_size' => env('RAJAUPLOAD_CHUNK_SIZE', 1024), // KB
        'temporary_directory' => env('RAJAUPLOAD_TEMP_DIR', 'livewire-tmp'),
    ],

    /*
    |--------------------------------------------------------------------------
    | File Naming Configuration
    |--------------------------------------------------------------------------
    */
    'naming' => [
        'pattern' => env('RAJAUPLOAD_NAMING_PATTERN', '{timestamp}_{random}_{original}'),
        'timestamp_format' => env('RAJAUPLOAD_TIMESTAMP_FORMAT', 'YmdHis'),
        'random_length' => env('RAJAUPLOAD_RANDOM_LENGTH', 8),
        'preserve_original' => env('RAJAUPLOAD_PRESERVE_ORIGINAL', true),
        'lowercase' => env('RAJAUPLOAD_LOWERCASE', true),
        'remove_spaces' => env('RAJAUPLOAD_REMOVE_SPACES', true),
        'allowed_characters' => env('RAJAUPLOAD_ALLOWED_CHARS', 'a-zA-Z0-9._-'),
    ],

    /*
    |--------------------------------------------------------------------------
    | Image Processing
    |--------------------------------------------------------------------------
    */
    'image' => [
        'driver' => env('RAJAUPLOAD_IMAGE_DRIVER', 'gd'), // gd or imagick
        'quality' => env('RAJAUPLOAD_IMAGE_QUALITY', 85),
        'auto_orient' => env('RAJAUPLOAD_AUTO_ORIENT', true),
        'strip_metadata' => env('RAJAUPLOAD_STRIP_METADATA', true),
        'progressive' => env('RAJAUPLOAD_PROGRESSIVE', true),
    ],

    /*
    |--------------------------------------------------------------------------
    | Thumbnail Configuration
    |--------------------------------------------------------------------------
    */
    'thumbnails' => [
        'enabled' => env('RAJAUPLOAD_THUMBNAILS_ENABLED', true),
        'directory' => env('RAJAUPLOAD_THUMBNAILS_DIR', 'thumbnails'),
        'prefix' => env('RAJAUPLOAD_THUMBNAILS_PREFIX', 'thumb_'),
        'suffix' => env('RAJAUPLOAD_THUMBNAILS_SUFFIX', ''),
        'quality' => env('RAJAUPLOAD_THUMBNAILS_QUALITY', 80),
        'fit' => env('RAJAUPLOAD_THUMBNAILS_FIT', 'crop'), // crop, contain, fill, stretch
        
        'sizes' => [
            'small' => [
                'width' => 150,
                'height' => 150,
                'quality' => 75,
            ],
            'medium' => [
                'width' => 300,
                'height' => 300,
                'quality' => 80,
            ],
            'large' => [
                'width' => 600,
                'height' => 600,
                'quality' => 85,
            ],
            // Percentage-based sizes
            '50p' => [
                'percentage' => 50,
                'quality' => 80,
            ],
            '25p' => [
                'percentage' => 25,
                'quality' => 75,
            ],
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | WebP Configuration
    |--------------------------------------------------------------------------
    */
    'webp' => [
        'enabled' => env('RAJAUPLOAD_WEBP_ENABLED', true),
        'quality' => env('RAJAUPLOAD_WEBP_QUALITY', 80),
        'generate_original' => env('RAJAUPLOAD_WEBP_ORIGINAL', true),
        'generate_thumbnails' => env('RAJAUPLOAD_WEBP_THUMBNAILS', true),
        'suffix' => env('RAJAUPLOAD_WEBP_SUFFIX', '.webp'),
    ],

    /*
    |--------------------------------------------------------------------------
    | File Type Restrictions
    |--------------------------------------------------------------------------
    */
    'file_types' => [
        'images' => [
            'extensions' => ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg', 'webp'],
            'mime_types' => [
                'image/jpeg',
                'image/png', 
                'image/gif',
                'image/bmp',
                'image/svg+xml',
                'image/webp'
            ],
            'max_size' => 10240, // KB
        ],
        'documents' => [
            'extensions' => ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt'],
            'mime_types' => [
                'application/pdf',
                'application/msword',
                'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                'application/vnd.ms-excel',
                'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                'application/vnd.ms-powerpoint',
                'application/vnd.openxmlformats-officedocument.presentationml.presentation',
                'text/plain'
            ],
            'max_size' => 51200, // KB (50MB)
        ],
        'videos' => [
            'extensions' => ['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm'],
            'mime_types' => [
                'video/mp4',
                'video/x-msvideo',
                'video/quicktime',
                'video/x-ms-wmv',
                'video/x-flv',
                'video/webm'
            ],
            'max_size' => 102400, // KB (100MB)
        ],
        'audio' => [
            'extensions' => ['mp3', 'wav', 'ogg', 'aac', 'flac'],
            'mime_types' => [
                'audio/mpeg',
                'audio/wav',
                'audio/ogg',
                'audio/aac',
                'audio/flac'
            ],
            'max_size' => 20480, // KB (20MB)
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Collection Presets
    |--------------------------------------------------------------------------
    */
    'collections' => [
        'basic' => [
            'directory' => 'basic',
            'max_files' => 1,
            'file_types' => ['images'],
            'thumbnails' => ['small', 'medium'],
            'webp' => true,
            'naming_pattern' => '{timestamp}_{random}_{original}',
        ],
        'products' => [
            'directory' => 'products',
            'max_files' => 10,
            'file_types' => ['images'],
            'thumbnails' => ['small', 'medium', 'large'],
            'webp' => true,
            'naming_pattern' => 'product_{timestamp}_{random}',
            'resize' => [
                'width' => 1200,
                'height' => 1200,
                'fit' => 'contain',
            ],
        ],
        'gallery' => [
            'directory' => 'gallery',
            'max_files' => 50,
            'file_types' => ['images'],
            'thumbnails' => ['small', 'medium', 'large'],
            'webp' => true,
            'naming_pattern' => 'gallery_{timestamp}_{random}',
            'reorderable' => true,
        ],
        'avatars' => [
            'directory' => 'avatars',
            'max_files' => 1,
            'file_types' => ['images'],
            'thumbnails' => ['small'],
            'webp' => true,
            'naming_pattern' => 'avatar_{user_id}_{timestamp}',
            'resize' => [
                'width' => 400,
                'height' => 400,
                'fit' => 'crop',
            ],
        ],
        'documents' => [
            'directory' => 'documents',
            'max_files' => 5,
            'file_types' => ['documents'],
            'thumbnails' => false,
            'webp' => false,
            'naming_pattern' => 'doc_{timestamp}_{random}_{original}',
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Security Settings
    |--------------------------------------------------------------------------
    */
    'security' => [
        'scan_uploads' => env('RAJAUPLOAD_SCAN_UPLOADS', false),
        'quarantine_suspicious' => env('RAJAUPLOAD_QUARANTINE', false),
        'allowed_domains' => env('RAJAUPLOAD_ALLOWED_DOMAINS', ''),
        'block_executable' => env('RAJAUPLOAD_BLOCK_EXECUTABLE', true),
        'validate_image_content' => env('RAJAUPLOAD_VALIDATE_IMAGE', true),
    ],

    /*
    |--------------------------------------------------------------------------
    | Performance Settings
    |--------------------------------------------------------------------------
    */
    'performance' => [
        'lazy_loading' => env('RAJAUPLOAD_LAZY_LOADING', true),
        'cache_thumbnails' => env('RAJAUPLOAD_CACHE_THUMBNAILS', true),
        'cache_duration' => env('RAJAUPLOAD_CACHE_DURATION', 3600), // seconds
        'optimize_images' => env('RAJAUPLOAD_OPTIMIZE_IMAGES', true),
        'parallel_processing' => env('RAJAUPLOAD_PARALLEL_PROCESSING', false),
    ],

    /*
    |--------------------------------------------------------------------------
    | UI/UX Settings
    |--------------------------------------------------------------------------
    */
    'ui' => [
        'show_file_info' => env('RAJAUPLOAD_SHOW_FILE_INFO', true),
        'show_progress' => env('RAJAUPLOAD_SHOW_PROGRESS', true),
        'show_thumbnails' => env('RAJAUPLOAD_SHOW_THUMBNAILS', true),
        'drag_drop' => env('RAJAUPLOAD_DRAG_DROP', true),
        'preview_modal' => env('RAJAUPLOAD_PREVIEW_MODAL', true),
        'edit_button' => env('RAJAUPLOAD_EDIT_BUTTON', true),
        'remove_button' => env('RAJAUPLOAD_REMOVE_BUTTON', true),
    ],

    /*
    |--------------------------------------------------------------------------
    | Logging Configuration
    |--------------------------------------------------------------------------
    */
    'logging' => [
        'enabled' => env('RAJAUPLOAD_LOGGING', true),
        'level' => env('RAJAUPLOAD_LOG_LEVEL', 'info'), // debug, info, warning, error
        'log_uploads' => env('RAJAUPLOAD_LOG_UPLOADS', true),
        'log_deletions' => env('RAJAUPLOAD_LOG_DELETIONS', true),
        'log_errors' => env('RAJAUPLOAD_LOG_ERRORS', true),
    ],

    /*
    |--------------------------------------------------------------------------
    | Cleanup Settings
    |--------------------------------------------------------------------------
    */
    'cleanup' => [
        'auto_cleanup' => env('RAJAUPLOAD_AUTO_CLEANUP', true),
        'cleanup_temp_files' => env('RAJAUPLOAD_CLEANUP_TEMP', true),
        'temp_file_lifetime' => env('RAJAUPLOAD_TEMP_LIFETIME', 24), // hours
        'cleanup_orphaned' => env('RAJAUPLOAD_CLEANUP_ORPHANED', true),
        'orphaned_check_interval' => env('RAJAUPLOAD_ORPHANED_INTERVAL', 168), // hours (1 week)
    ],
];
