<?php

namespace Modules\RajaCms\Filament\Resources\CmsResource\Forms;

use Modules\RajaCms\Filament\Resources\CmsResource;
use App\Helpers\FileHelper;
use App\Models\KategoriArtikel;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Radio;
use Filament\Forms\Components\Section;
use Illuminate\Support\Facades\Log;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Set;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Livewire\Features\SupportFileUploads\TemporaryUploadedFile;
use Modules\Rajapicker\Filament\Forms\Components\RajaPicker;

class InfoKonten
{
    public static function make(): Grid
    {
        return Grid::make('Info Konten')


            ->schema([
                TextInput::make('judul')
                    ->required()
                    ->live(onBlur: true)
                    ->afterStateUpdated(function (Set $set, ?string $state) {
                        $slug = Str::slug($state);
                        $uniqueSlug =  self::generateUniqueSlug($slug); // Using the full class path
                        $set('slug', $uniqueSlug);
                    })->columnSpanFull(),
                Select::make('kategori_id')
                    ->options(fn() => KategoriArtikel::get()->pluck('nama', 'id'))
                    ->visible(fn($get) => $get('jenis') == 'ARTIKEL')
                    ->relationship('kategoriArtikel', 'nama')
                    ->createOptionForm([
                        TextInput::make('nama')->label('nama kategori')
                            ->required(),
                    ])->columnSpanFull(),
                TextInput::make('slug')

                    ->label('slug')
                    // ->readOnly()
                    ->columnSpanFull()
                    ->helperText('Di generate otomatis ')
                    ->rules(['required', 'string', 'max:255']),

                Radio::make('status')
                    ->options(function ($get) {
                        $options = [
                            'draft' => 'Draft',
                            'tampil' => 'Tampil',
                        ];

                        // Hanya tampilkan opsi "home" jika jenis == "HALAMAN"
                        if ($get('jenis') === 'HALAMAN') {
                            $options['home'] = 'Jadikan halaman utama';
                        }

                        return $options;
                    })
                    ->default('tampil')
                    ->columnSpan(1)
                    ->live(),



                // RajaPicker::make('gambar')
                // ->visible(fn($get) => in_array($get('jenis'), ['ARTIKEL', 'HALAMAN', 'ACARA']))

                // ->label('gambar utama')
                // ->collection('cms')
                // ->previewSize(200)
                // ->columnSpanFull(),

 
                FileUpload::make('gambar')
                    ->label('gambar utama')
                    ->directory('cms')
                    ->image()
                    ->previewable(true)
                    ->openable(false)
                    ->downloadable(false)
                    ->deletable(true)
                    ->maxSize(10240) // 10MB
                    ->acceptedFileTypes(['image/jpeg', 'image/png', 'image/gif', 'image/webp'])
                    ->imagePreviewHeight('200')
                    ->columnSpanFull(),








            ]);
    }

    private static function generateUniqueSlug($slug)
    {
        $baseSlug = $slug;
        $count = 2;

        // Check if the slug already exists in the database
        while (DB::table('cms')->where('slug', $slug)->exists()) {
            $slug = $baseSlug . '-' . $count; // Add a number to the end of the slug
            $count++;
        }

        return $slug; // Return the unique slug
    }

    /**
     * Handle media library integration after save
     */
    public static function handleMediaLibraryAfterSave($record, array $data): void
    {
        try {
            // Handle gambar field using SimpanMedia trait
            if (isset($data['gambar']) && !empty($data['gambar'])) {
                // Gunakan method simpanMedia langsung pada record (model)
                $media = $record->simpanMedia(
                    fieldValue: $data['gambar'],
                    fieldName: 'gambar',
                    collection: 'cms',
                    options: [
                        'deleteExisting' => true // Hapus media lama jika ada
                    ]
                );

                if ($media) {
                    // Handle both single media object and array of media objects
                    $mediaId = is_array($media) ? (isset($media[0]) ? $media[0]->id : 'unknown') : $media->id;

                    Log::info('Media berhasil disimpan untuk CMS', [
                        'record_id' => $record->id,
                        'media_id' => $mediaId,
                        'field_name' => 'gambar',
                        'collection' => 'cms'
                    ]);
                }
            }
        } catch (\Exception $e) {
            Log::error('Error saat menyimpan media untuk CMS', [
                'record_id' => $record->id ?? null,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }
}
