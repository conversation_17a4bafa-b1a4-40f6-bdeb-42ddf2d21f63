# Trait SimpanMedia - RajaPicker Module

## Deskripsi

Trait `SimpanMedia` adalah utility trait yang memudahkan penyimpanan gambar dari FileUpload FilamentPHP ke Spatie Media Library dengan nama field upload yang dinamis.

## Lokasi File

```
Modules/Rajapicker/app/Traits/SimpanMedia.php
```

## Instalasi & Penggunaan

### 1. Persiapan Model

Model harus mengimplementasikan `HasMedia` dan menggunakan trait `InteractsWithMedia` dari <PERSON>tie Media Library:

```php
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Modules\Rajapicker\Traits\SimpanMedia;

class YourModel extends Model implements HasMedia
{
    use InteractsWithMedia, SimpanMedia;
    
    // ... kode model lainnya
}
```

### 2. Penggunaan Dasar

#### Simpan Single File

```php
// Di dalam method save/update model atau form
public function afterSave(): void
{
    // <PERSON>bil nilai field upload
    $gambarPath = $this->record->gambar; // dari FileUpload field
    
    // Simpan ke media library
    $media = $this->record->simpanMedia(
        fieldValue: $gambarPath,
        fieldName: 'gambar',
        collection: 'cms'
    );
}
```

#### Simpan Multiple Files

```php
// Untuk multiple file upload
public function afterSave(): void
{
    $galleryPaths = $this->record->gallery; // array paths
    
    $mediaItems = $this->record->simpanMedia(
        fieldValue: $galleryPaths,
        fieldName: 'gallery',
        collection: 'gallery'
    );
}
```

### 3. Penggunaan dengan Options

```php
// Dengan custom options
$media = $this->record->simpanMedia(
    fieldValue: $gambarPath,
    fieldName: 'gambar',
    collection: 'cms',
    options: [
        'original_name' => 'Custom Name',
        'file_name' => 'custom-filename.jpg',
        'custom_properties' => [
            'alt_text' => 'Deskripsi gambar',
            'caption' => 'Caption gambar'
        ]
    ]
);
```

### 4. Simpan dari UploadedFile

```php
// Langsung dari UploadedFile object
use Illuminate\Http\UploadedFile;

public function uploadFile(UploadedFile $file)
{
    $media = $this->simpanMediaDariUpload(
        uploadedFile: $file,
        fieldName: 'avatar',
        collection: 'avatars',
        options: [
            'custom_properties' => [
                'uploaded_by' => auth()->id()
            ]
        ]
    );
}
```

## Method yang Tersedia

### 1. `simpanMedia()`

Method utama untuk menyimpan file ke media library.

**Parameter:**
- `$fieldValue` (string|array): Path file atau array paths
- `$fieldName` (string): Nama field upload
- `$collection` (string): Nama collection media (default: 'default')
- `$options` (array): Opsi tambahan

**Return:** `Media|array|null`

### 2. `simpanMediaDariUpload()`

Simpan langsung dari UploadedFile object.

**Parameter:**
- `$uploadedFile` (UploadedFile): File yang diupload
- `$fieldName` (string): Nama field upload
- `$collection` (string): Nama collection media
- `$options` (array): Opsi tambahan

**Return:** `Media|null`

### 3. `hapusMedia()`

Hapus semua media dari collection tertentu.

**Parameter:**
- `$fieldName` (string): Nama field
- `$collection` (string): Nama collection

**Return:** `bool`

### 4. `getMediaUrl()`

Ambil URL media pertama dari collection.

**Parameter:**
- `$fieldName` (string): Nama field
- `$collection` (string): Nama collection
- `$conversion` (string): Nama conversion (optional)

**Return:** `string|null`

### 5. `getMediaUrls()`

Ambil semua URL media dari collection.

**Parameter:**
- `$fieldName` (string): Nama field
- `$collection` (string): Nama collection
- `$conversion` (string): Nama conversion (optional)

**Return:** `array`

## Contoh Implementasi Lengkap

### 1. Di Form Resource

```php
<?php

namespace App\Filament\Resources\CmsResource\Forms;

use Filament\Forms\Components\FileUpload;
use Filament\Forms\Form;

class InfoKonten
{
    public static function form(Form $form): Form
    {
        return $form->schema([
            FileUpload::make('gambar')
                ->label('Gambar Utama')
                ->directory('cms')
                ->image()
                ->columnSpanFull(),
                
            FileUpload::make('gallery')
                ->label('Galeri')
                ->directory('cms/gallery')
                ->image()
                ->multiple()
                ->reorderable()
                ->columnSpanFull(),
        ]);
    }
}
```

### 2. Di Resource Page

```php
<?php

namespace App\Filament\Resources\CmsResource\Pages;

use Filament\Resources\Pages\EditRecord;

class EditCms extends EditRecord
{
    protected function afterSave(): void
    {
        // Simpan gambar utama
        if ($this->record->gambar) {
            $this->record->simpanMedia(
                fieldValue: $this->record->gambar,
                fieldName: 'gambar',
                collection: 'cms'
            );
        }
        
        // Simpan gallery
        if ($this->record->gallery) {
            $this->record->simpanMedia(
                fieldValue: $this->record->gallery,
                fieldName: 'gallery',
                collection: 'gallery'
            );
        }
    }
}
```

### 3. Di Model untuk Accessor

```php
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Modules\Rajapicker\Traits\SimpanMedia;

class Cms extends Model implements HasMedia
{
    use InteractsWithMedia, SimpanMedia;
    
    // Accessor untuk mendapatkan URL gambar
    public function getGambarUrlAttribute(): ?string
    {
        return $this->getMediaUrl('gambar', 'cms');
    }
    
    // Accessor untuk mendapatkan semua URL gallery
    public function getGalleryUrlsAttribute(): array
    {
        return $this->getMediaUrls('gallery', 'gallery');
    }
}
```

## Options yang Tersedia

### Custom Properties

```php
$options = [
    'custom_properties' => [
        'alt_text' => 'Deskripsi gambar',
        'caption' => 'Caption gambar',
        'uploaded_by' => auth()->id(),
        'category' => 'banner'
    ]
];
```

### Custom Names

```php
$options = [
    'original_name' => 'Nama File Asli',
    'file_name' => 'nama-file-custom.jpg'
];
```

### Multiple Files Options

```php
$options = [
    'original_names' => ['File 1', 'File 2', 'File 3'],
    'file_names' => ['file1.jpg', 'file2.jpg', 'file3.jpg'],
    'custom_properties' => [
        'category' => 'gallery'
    ]
];
```

## Error Handling

Trait ini sudah dilengkapi dengan error handling dan logging:

- Log error jika file tidak ditemukan
- Log success ketika file berhasil disimpan
- Return null/false jika terjadi error
- Exception handling untuk semua operasi

## Path Resolution

Trait ini dapat menangani berbagai format path:

1. Full path absolut
2. Path relatif dari storage/app/public
3. Path dari livewire-tmp (temporary uploads)
4. Path dari storage/app

## Catatan Penting

1. **Model Requirements**: Model harus implement `HasMedia` dan use `InteractsWithMedia`
2. **Collections**: Pastikan collection sudah didefinisikan di model
3. **Storage**: File harus sudah tersimpan di storage sebelum dipindah ke media library
4. **Permissions**: Pastikan aplikasi memiliki permission untuk membaca/menulis file
5. **Cleanup**: Trait tidak otomatis menghapus file temporary, lakukan cleanup manual jika diperlukan

## Troubleshooting

### File Not Found Error

```php
// Cek apakah file ada sebelum simpan
if (Storage::disk('public')->exists($filePath)) {
    $media = $model->simpanMedia($filePath, 'gambar', 'cms');
}
```

### Permission Error

Pastikan storage directory memiliki permission yang benar:

```bash
chmod -R 755 storage/
chmod -R 755 public/storage/
```

### Memory Issues

Untuk file besar, pertimbangkan menggunakan queue:

```php
// Implementasi dengan queue akan ditambahkan di versi selanjutnya
```
