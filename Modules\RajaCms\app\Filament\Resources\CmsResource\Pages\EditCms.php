<?php

namespace Modules\RajaCms\Filament\Resources\CmsResource\Pages;

use Modules\RajaCms\Filament\Resources\CmsResource;
use Modules\RajaCms\Filament\Resources\CmsResource\Forms\InfoKonten;
use Modules\RajaJson\Filament\Resources\Pages\AutoRajajsonEditRecord;
use Filament\Actions;

class EditCms extends AutoRajajsonEditRecord
{
    protected static string $resource = CmsResource::class;

    protected function mutateFormDataBeforeFill(array $data): array
    {
        // Load rajajson data dulu
        $data = parent::mutateFormDataBeforeFill($data);

        // Kemudian tambahkan data JSON (untuk jcol fields)
        if ($this->record) {
            $jcolData = $this->record->getJcolFormData();
            $data = array_merge($data, $jcolData);
        }

        return $data;
    }

    /**
     * Tambahkan tombol save di header (di atas form)
     */
    protected function getHeaderActions(): array
    {
        return [
            Actions\Action::make('simpanPerubahan')
                ->label('Simpan Perubahan')
                ->color('success')
                ->icon('heroicon-o-check-circle')
                ->action(function () {
                    $this->save();
                })
                ->extraAttributes([
                    'wire:loading.attr' => 'disabled',
                    'wire:loading.class' => 'opacity-70 cursor-wait',
                    'class' => 'filament-button-tunggu'
                ])
                ->iconPosition('after'),
        ];
    }

    /**
     * Kustomisasi tombol form actions (di bawah form)
     */
    protected function getFormActions(): array
    {
        return [
            $this->getSaveFormAction(),
            $this->getCancelFormAction(),
        ];
    }

    /**
     * Kustomisasi tombol save dengan indikator loading
     */
    protected function getSaveFormAction(): Actions\Action
    {
        return parent::getSaveFormAction()
            ->label('Simpan Perubahan')
            ->color('success')
            ->icon('heroicon-o-check-circle')
            ->extraAttributes([
                'wire:loading.attr' => 'disabled',
                'wire:loading.class' => 'opacity-70 cursor-wait',
                'class' => 'filament-button-tunggu'
            ])
            ->iconPosition('after');
    }

    /**
     * Handle media library integration after save
     */
    protected function afterSave(): void
    {
        parent::afterSave();

        // Call media library integration
        InfoKonten::handleMediaLibraryAfterSave($this->record, $this->data);
    }

    // public function getRedirectUrl(): string
    // {
        // return CmsResource::getUrl('index');
    // }
}
