<?php

return [
    /*
    |--------------------------------------------------------------------------
    | SimpanMedia Trait Configuration
    |--------------------------------------------------------------------------
    |
    | Konfigurasi untuk trait SimpanMedia yang digunakan untuk menyimpan
    | file upload dari FilamentPHP ke Spatie Media Library
    |
    */

    /*
    |--------------------------------------------------------------------------
    | Default Settings
    |--------------------------------------------------------------------------
    |
    | Pengaturan default untuk trait SimpanMedia
    |
    */
    'defaults' => [
        // Collection default untuk media
        'collection' => 'default',
        
        // Disk storage yang digunakan
        'disk' => 'public',
        
        // Hapus media yang sudah ada sebelumnya saat upload baru
        'delete_existing' => true,
        
        // Preserve original file name
        'preserve_original_name' => false,
        
        // Generate unique file names
        'generate_unique_names' => true,
        
        // Logging untuk debugging
        'enable_logging' => true,
        
        // Validasi file sebelum upload
        'validate_files' => true,
    ],

    /*
    |--------------------------------------------------------------------------
    | File Validation
    |--------------------------------------------------------------------------
    |
    | Aturan validasi untuk file yang diupload
    |
    */
    'validation' => [
        // Tipe MIME yang diizinkan
        'allowed_mime_types' => [
            'image/jpeg',
            'image/jpg', 
            'image/png',
            'image/gif',
            'image/webp',
            'image/svg+xml',
            'image/bmp',
            'image/tiff',
            'image/vnd.microsoft.icon', // untuk .ico files
        ],
        
        // Ekstensi file yang diizinkan
        'allowed_extensions' => [
            'jpg', 'jpeg', 'png', 'gif', 'webp', 'svg', 'bmp', 'tiff', 'ico'
        ],
        
        // Ukuran maksimal file dalam bytes (default: 10MB)
        'max_file_size' => 10485760,
        
        // Ukuran minimal file dalam bytes (default: 1KB)
        'min_file_size' => 1024,
        
        // Validasi dimensi gambar
        'image_dimensions' => [
            'min_width' => 10,
            'min_height' => 10,
            'max_width' => 5000,
            'max_height' => 5000,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | File Naming Convention
    |--------------------------------------------------------------------------
    |
    | Pengaturan untuk penamaan file yang diupload
    |
    */
    'file_naming' => [
        // Gunakan ULID untuk nama file unik
        'use_ulid' => true,
        
        // Preserve original extension
        'preserve_extension' => true,
        
        // Convert to lowercase
        'lowercase' => true,
        
        // Replace spaces with underscores
        'replace_spaces' => true,
        
        // Remove special characters
        'remove_special_chars' => true,
        
        // Pattern untuk nama file custom
        // Placeholder: {ulid}, {original}, {timestamp}, {random}
        'custom_pattern' => '{ulid}',
    ],

    /*
    |--------------------------------------------------------------------------
    | Storage Paths
    |--------------------------------------------------------------------------
    |
    | Konfigurasi path untuk penyimpanan file
    |
    */
    'storage' => [
        // Base path untuk uploads
        'base_path' => 'uploads',
        
        // Path berdasarkan collection
        'collection_paths' => [
            'default' => 'default',
            'cms' => 'cms',
            'produk' => 'produk',
            'gallery' => 'gallery',
            'banner' => 'banner',
            'avatar' => 'avatar',
            'documents' => 'documents',
        ],
        
        // Buat subdirectory berdasarkan tanggal
        'date_subdirectory' => false,
        
        // Format subdirectory tanggal (Y/m/d, Y-m, dll)
        'date_format' => 'Y/m',
    ],

    /*
    |--------------------------------------------------------------------------
    | Media Conversions
    |--------------------------------------------------------------------------
    |
    | Konfigurasi untuk konversi media (thumbnail, resize, dll)
    |
    */
    'conversions' => [
        // Aktifkan konversi otomatis
        'auto_generate' => true,
        
        // Konversi yang tersedia
        'available' => [
            'thumb' => [
                'width' => 150,
                'height' => 150,
                'quality' => 80,
                'format' => 'webp',
                'fit' => 'crop',
            ],
            'medium' => [
                'width' => 300,
                'height' => 300,
                'quality' => 85,
                'format' => 'webp',
                'fit' => 'contain',
            ],
            'large' => [
                'width' => 800,
                'height' => 600,
                'quality' => 90,
                'format' => 'webp',
                'fit' => 'contain',
            ],
        ],
        
        // Konversi default yang selalu dibuat
        'default_conversions' => ['thumb'],
    ],

    /*
    |--------------------------------------------------------------------------
    | Error Handling
    |--------------------------------------------------------------------------
    |
    | Pengaturan untuk penanganan error
    |
    */
    'error_handling' => [
        // Throw exception saat error
        'throw_exceptions' => false,
        
        // Log errors
        'log_errors' => true,
        
        // Log channel untuk error
        'log_channel' => 'daily',
        
        // Return null saat error (jika false, akan return false)
        'return_null_on_error' => true,
    ],

    /*
    |--------------------------------------------------------------------------
    | Performance Settings
    |--------------------------------------------------------------------------
    |
    | Pengaturan untuk optimasi performa
    |
    */
    'performance' => [
        // Queue untuk processing media yang berat
        'use_queue' => false,
        
        // Queue name
        'queue_name' => 'media-processing',
        
        // Batch processing untuk multiple files
        'batch_processing' => true,
        
        // Maksimal file per batch
        'max_batch_size' => 10,
    ],

    /*
    |--------------------------------------------------------------------------
    | Custom Properties
    |--------------------------------------------------------------------------
    |
    | Properties custom yang dapat ditambahkan ke media
    |
    */
    'custom_properties' => [
        // Properties default yang selalu ditambahkan
        'default' => [
            'uploaded_by' => 'system',
            'source' => 'simpan_media_trait',
        ],
        
        // Properties berdasarkan collection
        'by_collection' => [
            'cms' => [
                'category' => 'content',
                'public' => true,
            ],
            'produk' => [
                'category' => 'product',
                'public' => true,
            ],
            'avatar' => [
                'category' => 'user',
                'public' => false,
            ],
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Cleanup Settings
    |--------------------------------------------------------------------------
    |
    | Pengaturan untuk pembersihan file
    |
    */
    'cleanup' => [
        // Auto cleanup temporary files
        'auto_cleanup_temp' => true,
        
        // Cleanup orphaned media (media tanpa model)
        'cleanup_orphaned' => false,
        
        // Cleanup old conversions saat media dihapus
        'cleanup_conversions' => true,
        
        // Cleanup empty directories
        'cleanup_empty_dirs' => true,
    ],
];
