# Contoh <PERSON><PERSON><PERSON>an <PERSON>load Field

## 1. <PERSON><PERSON><PERSON><PERSON>

### Single Image Upload
```php
use <PERSON><PERSON><PERSON>\Rajapicker\Filament\Forms\Components\RajaUpload;

RajaUpload::make('featured_image')
    ->label('<PERSON><PERSON>bar <PERSON>')
    ->collection('default')
```

### Multiple Image Upload
```php
RajaUpload::make('gallery_images')
    ->label('Galeri Gambar')
    ->multiple()
    ->maxFiles(10)
    ->collection('gallery')
```

## 2. Preset Methods (Recommended)

### Product Images
```php
// Setup lengkap untuk gambar produk
RajaUpload::make('product_images')
    ->label('Gambar Produk')
    ->productImage()
    ->multiple()
    ->maxFiles(5)
```

### User Avatar
```php
// Setup lengkap untuk avatar user
RajaUpload::make('avatar')
    ->label('Avatar')
    ->userAvatar()
```

### Banner/Hero Images
```php
// Setup lengkap untuk banner
RajaUpload::make('hero_banner')
    ->label('Hero Banner')
    ->bannerImage()
```

### Gallery Collection
```php
// Setup lengkap untuk galeri
RajaUpload::make('photo_gallery')
    ->label('Galeri Foto')
    ->galleryImages(20) // max 20 files
```

### Document Upload
```php
// Setup untuk upload dokumen
RajaUpload::make('attachments')
    ->label('Lampiran Dokumen')
    ->document()
    ->multiple()
    ->maxFiles(5)
```

## 3. Custom Configuration

### Advanced Image Setup
```php
RajaUpload::make('portfolio_images')
    ->label('Portfolio Images')
    ->collection('portfolio')
    ->imageEditor()
    ->commonAspectRatios()
    ->imageResizePreset('large')
    ->generateThumbnails()
    ->convertToWebp()
    ->multiple()
    ->reorderable()
    ->maxFiles(15)
    ->panelLayout('grid')
```

### Social Media Optimized
```php
RajaUpload::make('social_images')
    ->label('Social Media Images')
    ->collection('social')
    ->imageEditor()
    ->socialMediaAspectRatios()
    ->imageResizePreset('hd')
    ->generateThumbnails()
    ->convertToWebp()
    ->multiple()
    ->maxFiles(10)
```

### Custom Aspect Ratios
```php
RajaUpload::make('custom_banner')
    ->label('Custom Banner')
    ->collection('banners')
    ->imageEditor()
    ->imageEditorAspectRatios([
        '21:9',  // Ultra wide
        '16:9',  // Standard wide
        '3:1',   // Super wide banner
        '2:1',   // Wide banner
    ])
    ->imageResizePreset('4k')
    ->generateThumbnails()
```

## 4. Form Resource Examples

### Blog Post Form
```php
use Modules\Rajapicker\Filament\Forms\Components\RajaUpload;

public static function form(Form $form): Form
{
    return $form->schema([
        TextInput::make('title')
            ->label('Judul')
            ->required(),
            
        // Featured image untuk blog post
        RajaUpload::make('featured_image')
            ->label('Gambar Utama')
            ->productImage()
            ->required(),
            
        // Gallery untuk blog post
        RajaUpload::make('gallery')
            ->label('Galeri Foto')
            ->galleryImages(10)
            ->columnSpanFull(),
            
        Textarea::make('content')
            ->label('Konten')
            ->required(),
    ]);
}
```

### Product Form
```php
public static function form(Form $form): Form
{
    return $form->schema([
        TextInput::make('name')
            ->label('Nama Produk')
            ->required(),
            
        // Main product image
        RajaUpload::make('main_image')
            ->label('Gambar Utama')
            ->productImage()
            ->required(),
            
        // Product gallery
        RajaUpload::make('gallery_images')
            ->label('Galeri Produk')
            ->productImage()
            ->multiple()
            ->maxFiles(8)
            ->reorderable(),
            
        // Product documents (specs, manual, etc)
        RajaUpload::make('documents')
            ->label('Dokumen Produk')
            ->document(['application/pdf'])
            ->multiple()
            ->maxFiles(3),
    ]);
}
```

### User Profile Form
```php
public static function form(Form $form): Form
{
    return $form->schema([
        // User avatar
        RajaUpload::make('avatar')
            ->label('Avatar')
            ->userAvatar(),
            
        // Cover photo
        RajaUpload::make('cover_photo')
            ->label('Foto Cover')
            ->bannerImage(),
            
        TextInput::make('name')
            ->label('Nama')
            ->required(),
            
        // Portfolio images
        RajaUpload::make('portfolio')
            ->label('Portfolio')
            ->galleryImages(15)
            ->columnSpanFull(),
    ]);
}
```

## 5. Advanced Customization

### Custom File Naming
```php
RajaUpload::make('custom_files')
    ->collection('custom')
    ->getUploadedFileNameForStorageUsing(
        fn (TemporaryUploadedFile $file): string => 
            'custom_' . time() . '_' . $file->getClientOriginalName()
    )
```

### Custom Thumbnail Sizes
```php
RajaUpload::make('images')
    ->collection('custom')
    ->generateThumbnails()
    ->thumbnailSizes([
        'tiny' => ['width' => 50, 'height' => 50],
        'small' => ['width' => 150, 'height' => 150],
        'medium' => ['width' => 300, 'height' => 300],
        'large' => ['width' => 600, 'height' => 600],
        'xl' => ['width' => 1200, 'height' => 1200],
    ])
```

### S3 Storage Configuration
```php
RajaUpload::make('cloud_images')
    ->label('Cloud Images')
    ->disk('s3')
    ->directory('uploads/images')
    ->collection('cloud')
    ->productImage()
    ->visibility('public')
```

## 6. Integration with Models

### Model Setup
```php
// Model
class Product extends Model
{
    protected $fillable = [
        'name',
        'main_image',
        'gallery_images',
        'documents',
    ];
    
    protected $casts = [
        'gallery_images' => 'array',
        'documents' => 'array',
    ];
    
    // Accessor untuk URL gambar utama
    public function getMainImageUrlAttribute()
    {
        return $this->main_image ? Storage::url($this->main_image) : '/noimage.jpg';
    }
    
    // Accessor untuk URL galeri
    public function getGalleryUrlsAttribute()
    {
        if (!$this->gallery_images) return [];
        
        return collect($this->gallery_images)->map(function ($path) {
            return Storage::url($path);
        })->toArray();
    }
    
    // Accessor untuk thumbnail
    public function getMainImageThumbnailAttribute()
    {
        if (!$this->main_image) return '/noimage.jpg';
        
        $pathInfo = pathinfo($this->main_image);
        $thumbnailPath = $pathInfo['dirname'] . '/thumbnails/' . 
                        $pathInfo['filename'] . '_th.webp';
                        
        return Storage::exists($thumbnailPath) ? 
               Storage::url($thumbnailPath) : 
               Storage::url($this->main_image);
    }
}
```

### Blade Template Usage
```blade
{{-- Display main image --}}
<img src="{{ $product->main_image_url }}" alt="{{ $product->name }}">

{{-- Display thumbnail --}}
<img src="{{ $product->main_image_thumbnail }}" alt="{{ $product->name }}">

{{-- Display gallery --}}
@foreach($product->gallery_urls as $imageUrl)
    <img src="{{ $imageUrl }}" alt="{{ $product->name }}">
@endforeach
```

## 7. Validation Examples

### Custom Validation Rules
```php
RajaUpload::make('profile_image')
    ->label('Profile Image')
    ->userAvatar()
    ->rules([
        'required',
        'image',
        'mimes:jpeg,png,jpg',
        'max:2048', // 2MB
        'dimensions:min_width=100,min_height=100,max_width=2000,max_height=2000'
    ])
```

### Conditional Validation
```php
RajaUpload::make('featured_image')
    ->label('Featured Image')
    ->productImage()
    ->required(fn (Get $get) => $get('type') === 'featured')
    ->visible(fn (Get $get) => in_array($get('category'), ['blog', 'news']))
```

## 8. Performance Optimization

### Lazy Loading Setup
```php
RajaUpload::make('large_gallery')
    ->label('Large Gallery')
    ->galleryImages(50)
    ->fetchFileInformation(false) // Disable file info fetching for performance
    ->previewable(false) // Disable preview for large files
```

### Optimized for Mobile
```php
RajaUpload::make('mobile_images')
    ->label('Mobile Images')
    ->collection('mobile')
    ->imageResizePreset('small') // Smaller size for mobile
    ->generateThumbnails()
    ->convertToWebp() // Better compression
    ->maxSize(1024) // 1MB max for mobile
```
